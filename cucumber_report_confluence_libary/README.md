# Cucumber Report Confluence Library

A libary to create a report component test to confluence page.

## Features

- Parse Cucumber JSON reports
- Generate beautiful HTML reports with statistics
- Publish reports directly to Confluence
- Calculate test statistics (pass/fail rates, execution times)
- Configurable Confluence integration
- Comprehensive test coverage

## Requirements

- Java 17 or higher
- Gradle 7.0 or higher
- Confluence Cloud or Server with REST API access

## Installation

### Gradle

```gradle
dependencies {
    implementation 'com.library:cucumber-report-confluence-library:1.0.0'
}
```

### Maven

```xml
<dependency>
    <groupId>com.library</groupId>
    <artifactId>cucumber-report-confluence-library</artifactId>
    <version>1.0.0</version>
</dependency>
```

## Quick Start

### 1. Basic Usage

```java
import com.library.CucumberReportConfluenceIntegration;
import com.library.config.ConfluenceConfig;

// Configure Confluence
ConfluenceConfig config = new ConfluenceConfig();
config.setBaseUrl("https://your-confluence.atlassian.net");
config.setUsername("<EMAIL>");
config.setApiToken("your-api-token");
config.setSpaceKey("YOUR_SPACE");
config.setPageTitle("Cucumber Test Report");

// Create integration instance
CucumberReportConfluenceIntegration integration = new CucumberReportConfluenceIntegration()
        .withConfluenceConfig(config);

// Generate and publish report
String pageId = integration.generateAndPublishReport("path/to/cucumber.json");
System.out.println("Report published with page ID: " + pageId);

// Clean up
integration.close();
```

### 2. Generate HTML Only

```java
CucumberReportConfluenceIntegration integration = new CucumberReportConfluenceIntegration();
String htmlReport = integration.generateHtmlReport("path/to/cucumber.json");
// Save or use the HTML report as needed
```

### 3. Get Report Statistics

```java
CucumberReportConfluenceIntegration.ReportStatistics stats = 
    integration.getReportStatistics("path/to/cucumber.json");

System.out.println("Total Features: " + stats.getTotalFeatures());
System.out.println("Total Scenarios: " + stats.getTotalScenarios());
System.out.println("Success Rate: " + stats.getSuccessRate() + "%");
```

## Configuration

### Confluence Configuration

```java
ConfluenceConfig config = new ConfluenceConfig();

// Required settings
config.setBaseUrl("https://your-confluence.atlassian.net");
config.setUsername("<EMAIL>");
config.setApiToken("your-api-token");
config.setSpaceKey("YOUR_SPACE");
config.setPageTitle("Test Report");

// Optional settings
config.setParentPageId("*********"); // Create as child page
config.setCreatePageIfNotExists(true); // Default: true
config.setConnectionTimeout(30000); // Default: 30 seconds
config.setReadTimeout(60000); // Default: 60 seconds
```

### Getting Confluence API Token

1. Go to [Atlassian Account Settings](https://id.atlassian.com/manage-profile/security/api-tokens)
2. Click "Create API token"
3. Give it a label and click "Create"
4. Copy the token and use it in your configuration

## Cucumber JSON Format

This library expects Cucumber JSON reports in the standard format. To generate compatible JSON reports:

### With Cucumber JVM

```java
@CucumberOptions(
    plugin = {"json:target/cucumber-reports/cucumber.json"}
)
```

### With Maven Surefire

```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-surefire-plugin</artifactId>
    <configuration>
        <systemPropertyVariables>
            <cucumber.plugin>json:target/cucumber-reports/cucumber.json</cucumber.plugin>
        </systemPropertyVariables>
    </configuration>
</plugin>
```

## Report Features

The generated HTML reports include:

- **Summary Dashboard**: Overview of test execution
- **Statistics**: Pass/fail rates, execution times
- **Feature Breakdown**: Detailed results per feature
- **Scenario Details**: Step-by-step execution results
- **Visual Indicators**: Color-coded status indicators
- **Responsive Design**: Works on desktop and mobile

## Advanced Usage

### Custom Page Title

```java
String pageId = integration.generateAndPublishReport(
    "cucumber.json", 
    "Custom Report Title - " + LocalDate.now()
);
```

### Error Handling

```java
try {
    integration.generateAndPublishReport("cucumber.json");
} catch (IOException e) {
    logger.error("Failed to generate report", e);
    // Handle error appropriately
}
```

### Batch Processing

```java
String[] reportFiles = {"feature1.json", "feature2.json", "feature3.json"};
for (String file : reportFiles) {
    try {
        integration.generateAndPublishReport(file, "Report for " + file);
    } catch (Exception e) {
        logger.error("Failed to process " + file, e);
    }
}
```

## Building from Source

[TBU]

## Running Tests

```bash
./gradlew test 
```

or

```bash
./gradle test # if you install the gradle command in your computer
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the Apache License 2.0 - see the [LICENSE](http://www.apache.org/licenses/LICENSE-2.0.txt) file for details.

## Support

For issues and questions:

[TBU]

## Changelog

### Version 1.0.0
- Initial release
- Cucumber JSON parsing
- HTML report generation
- Confluence integration
- Comprehensive test coverage
