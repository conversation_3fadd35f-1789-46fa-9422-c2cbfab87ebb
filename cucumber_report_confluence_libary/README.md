# Cucumber Report Confluence Library

[![Java](https://img.shields.io/badge/Java-17+-orange.svg)](https://www.oracle.com/java/)
[![Gradle](https://img.shields.io/badge/Gradle-7.0+-blue.svg)](https://gradle.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

A powerful Java library for generating beautiful Cucumber test reports and publishing them directly to Confluence pages. This library parses Cucumber JSON reports and creates visually appealing, interactive reports with detailed test statistics and results.

## 🚀 Features

- **Parse Cucumber JSON Reports**: Convert Cucumber JSON output into structured report data
- **Generate HTML Reports**: Create beautiful, responsive HTML reports with detailed statistics
- **Confluence Integration**: Automatically publish reports to Confluence pages with native formatting
- **Multiple Authentication Methods**: Support for API Token and Bearer token authentication
- **Flexible Configuration**: Customizable page titles, spaces, and report formatting
- **Rich Report Content**: Includes test summaries, feature breakdowns, scenario details, and step-by-step results
- **Auto Page Management**: Automatically create or update Confluence pages
- **Beautiful Confluence Formatting**: Native Confluence macros and styling for professional reports

## 📋 Requirements

- Java 17 or higher
- Gradle 7.0 or higher
- Confluence Cloud or Server instance
- Valid Confluence API credentials

## 🛠️ Installation

### Using Gradle

Add the following to your `build.gradle`:

```gradle
dependencies {
    implementation 'com.library:cucumber-report-confluence:1.0.0'
}
```

### Manual Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd cucumber_report_confluence_libary
```

2. Build the library:
```bash
./gradlew build
```

3. Include the JAR in your project's classpath.

## 🔧 Configuration

### Confluence Configuration

Create a `ConfluenceConfig` object with your Confluence settings:

```java
ConfluenceConfig config = new ConfluenceConfig(
    "https://your-domain.atlassian.net/wiki",  // Confluence base URL
    "<EMAIL>",                   // Your Atlassian account email
    "your-api-token",                          // API token
    "YOUR_SPACE_KEY",                          // Confluence space key
    "Bearer"                                   // Token type (Bearer or Basic)
);

// Optional settings
config.setPageTitle("Cucumber Test Report");
config.setParentPageId("*********");           // Optional parent page
config.setCreatePageIfNotExists(true);         // Auto-create pages
config.setConnectionTimeout(30000);            // 30 seconds
config.setReadTimeout(60000);                  // 60 seconds
```

### Authentication Methods

#### API Token Authentication (Recommended)
```java
ConfluenceConfig config = new ConfluenceConfig(
    "https://your-domain.atlassian.net/wiki",
    "<EMAIL>",
    "ATATT3xFfGF0...",  // Your API token
    "SPACE_KEY",
    "Bearer"
);
```

#### Basic Authentication
```java
ConfluenceConfig config = new ConfluenceConfig(
    "https://your-domain.atlassian.net/wiki",
    "<EMAIL>",
    "your-password-or-token",
    "SPACE_KEY",
    "Basic"
);
```

## 📖 Usage

### Basic Usage

```java
import com.library.CucumberReportConfluenceIntegration;
import com.library.config.ConfluenceConfig;

public class ReportExample {
    public static void main(String[] args) throws Exception {
        // Configure Confluence settings
        ConfluenceConfig config = new ConfluenceConfig(
            "https://your-domain.atlassian.net/wiki",
            "<EMAIL>",
            "your-api-token",
            "YOUR_SPACE",
            "Bearer"
        );
        config.setPageTitle("My Cucumber Test Report");
        
        // Create integration instance
        CucumberReportConfluenceIntegration integration = 
            new CucumberReportConfluenceIntegration()
                .withConfluenceConfig(config);
        
        // Generate and publish report
        String pageId = integration.generateAndPublishReport("path/to/cucumber.json");
        System.out.println("Report published! Page ID: " + pageId);
        
        // Clean up resources
        integration.close();
    }
}
```

### Advanced Usage

#### Generate HTML Report Only
```java
CucumberReportConfluenceIntegration integration = 
    new CucumberReportConfluenceIntegration();

String htmlReport = integration.generateHtmlReport("cucumber.json");
// Save or process HTML as needed
```

#### Generate Confluence-Formatted Report
```java
String confluenceContent = integration.generateConfluenceReport("cucumber.json");
// Use the Confluence-formatted content directly
```

#### Get Report Statistics
```java
CucumberReportConfluenceIntegration.ReportStatistics stats = 
    integration.getReportStatistics("cucumber.json");

System.out.println("Total Features: " + stats.getTotalFeatures());
System.out.println("Total Scenarios: " + stats.getTotalScenarios());
System.out.println("Success Rate: " + stats.getSuccessRate() + "%");
```

#### Custom Page Title
```java
String pageId = integration.generateAndPublishReport(
    "cucumber.json", 
    "Custom Report Title - " + LocalDateTime.now()
);
```

## 📊 Report Features

### Summary Section
- Total features and scenarios count
- Pass/fail statistics with visual indicators
- Success rate calculation
- Execution timestamp

### Feature Details
- Feature name and description
- Scenario breakdown with status
- Individual step results
- Execution duration
- Error details for failed tests

### Confluence Enhancements
- Native Confluence status macros (✅ ❌ ⚠️)
- Collapsible sections for better organization
- Color-coded results
- Professional table formatting
- Interactive elements

## 🔍 Example Output

The library generates comprehensive reports including:

```
📊 Test Summary
┌─────────────────┬───────┬────────┐
│ Metric          │ Value │ Status │
├─────────────────┼───────┼────────┤
│ Total Features  │   5   │   ✅   │
│ Total Scenarios │  23   │   ✅   │
│ Passed         │  20   │   ✅   │
│ Failed         │   3   │   ❌   │
│ Success Rate   │ 87.0% │   ⚠️   │
└─────────────────┴───────┴────────┘

🎯 Feature: User Authentication
   Description: Test user login and authentication flows
   Scenarios: 5 | Passed: 4 | Failed: 1 | Success Rate: 80.0%
   
   ✅ Scenario: Valid user login
   ❌ Scenario: Invalid password login
   ...
```

## 🧪 Testing

Run the test suite:

```bash
./gradlew test
```

Run with coverage:

```bash
./gradlew test jacocoTestReport
```

## 📁 Project Structure

```
cucumber_report_confluence_libary/
├── src/main/java/com/library/
│   ├── CucumberReportConfluenceIntegration.java  # Main integration class
│   ├── config/
│   │   └── ConfluenceConfig.java                 # Configuration class
│   ├── confluence/client/
│   │   └── ConfluenceClient.java                 # Confluence API client
│   ├── cucumber/report/
│   │   └── CucumberReportGenerator.java          # Report generation logic
│   ├── model/                                    # Data models
│   └── example/                                  # Usage examples
├── src/test/java/                                # Unit tests
├── build.gradle                                  # Build configuration
└── README.md                                     # This file
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the [examples](src/main/java/com/library/example/) directory
- Review the test cases for usage patterns

## 🔄 Changelog

### Version 1.0.0
- Initial release
- Cucumber JSON parsing
- HTML report generation
- Confluence integration
- API Token authentication
- Comprehensive test coverage
