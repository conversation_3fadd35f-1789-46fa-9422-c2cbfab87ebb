[{"line": 2, "elements": [{"start_timestamp": "2025-07-22T10:28:34.580Z", "before": [{"result": {"duration": 8402000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 11953000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 17, "name": "Successfully init step-up authentication session - Single Factor", "description": "", "id": "component-test-for-success-case-with-step-up-api---corrected-given-when-then-structure;successfully-init-step-up-authentication-session---single-factor;;2", "after": [{"result": {"duration": 38519000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 1627000, "status": "passed"}, "line": 10, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 8923000, "status": "passed"}, "line": 11, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 77919000, "status": "passed"}, "line": 12, "name": "I prepare the request for single factor OTP with flow id 123, profile type PROFILE_ID, and authConfig /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 40}, {"val": "123", "offset": 57}, {"val": "PROFILE_ID", "offset": 75}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 102}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.prepareRequestForSingleFactor(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1296571000, "status": "passed"}, "line": 13, "name": "The customer executes step-up initialization", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.customerExecutesStepUpInitialization()"}, "keyword": "When "}, {"result": {"duration": 9224000, "status": "passed"}, "line": 14, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpInit"}]}, {"start_timestamp": "2025-07-22T10:28:37.205Z", "before": [{"result": {"duration": 730000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 753000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 31, "name": "Successfully init step-up authentication session - With Fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api---corrected-given-when-then-structure;successfully-init-step-up-authentication-session---with-fallback;;2", "after": [{"result": {"duration": 57179000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 841000, "status": "passed"}, "line": 24, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 606000, "status": "passed"}, "line": 25, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 10121000, "status": "passed"}, "line": 26, "name": "I prepare the request with fallback for factor OTP, flow id 123, profile type PROFILE_ID, and authConfig /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 47}, {"val": "123", "offset": 60}, {"val": "PROFILE_ID", "offset": 78}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 105}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.prepareRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 107980000, "status": "passed"}, "line": 27, "name": "The customer executes step-up initialization", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.customerExecutesStepUpInitialization()"}, "keyword": "When "}, {"result": {"duration": 1949000, "status": "passed"}, "line": 28, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpInit"}]}]}]