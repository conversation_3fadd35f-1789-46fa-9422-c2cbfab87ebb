package com.library.config;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class ConfluenceConfigTest {

    @Test
    void shouldCreateConfigWithConstructor() {
        // Given
        String baseUrl = "https://confluence.example.com";
        String username = "<EMAIL>";
        String apiToken = "api-token";
        String spaceKey = "SPACE";

        // When
        ConfluenceConfig config = new ConfluenceConfig(baseUrl, username, apiToken, spaceKey);

        // Then
        assertThat(config.getBaseUrl()).isEqualTo(baseUrl);
        assertThat(config.getUsername()).isEqualTo(username);
        assertThat(config.getApiToken()).isEqualTo(apiToken);
        assertThat(config.getSpaceKey()).isEqualTo(spaceKey);
    }

    @Test
    void shouldSetAndGetProperties() {
        // Given
        ConfluenceConfig config = new ConfluenceConfig();
        
        // When
        config.setBaseUrl("https://confluence.example.com");
        config.setUsername("<EMAIL>");
        config.setApiToken("api-token");
        config.setSpaceKey("SPACE");
        config.setPageTitle("Test Page");
        config.setParentPageId("12345");
        config.setCreatePageIfNotExists(false);
        config.setConnectionTimeout(15000);
        config.setReadTimeout(30000);

        // Then
        assertThat(config.getBaseUrl()).isEqualTo("https://confluence.example.com");
        assertThat(config.getUsername()).isEqualTo("<EMAIL>");
        assertThat(config.getApiToken()).isEqualTo("api-token");
        assertThat(config.getSpaceKey()).isEqualTo("SPACE");
        assertThat(config.getPageTitle()).isEqualTo("Test Page");
        assertThat(config.getParentPageId()).isEqualTo("12345");
        assertThat(config.isCreatePageIfNotExists()).isFalse();
        assertThat(config.getConnectionTimeout()).isEqualTo(15000);
        assertThat(config.getReadTimeout()).isEqualTo(30000);
    }

    @Test
    void shouldValidateConfigSuccessfully() {
        // Given
        ConfluenceConfig config = new ConfluenceConfig();
        config.setBaseUrl("https://confluence.example.com");
        config.setUsername("<EMAIL>");
        config.setApiToken("api-token");
        config.setSpaceKey("SPACE");
        config.setPageTitle("Test Page");

        // When & Then
        config.validate(); // Should not throw exception
    }

    @Test
    void shouldThrowExceptionWhenBaseUrlIsMissing() {
        // Given
        ConfluenceConfig config = new ConfluenceConfig();
        config.setUsername("<EMAIL>");
        config.setApiToken("api-token");
        config.setSpaceKey("SPACE");
        config.setPageTitle("Test Page");

        // When & Then
        assertThatThrownBy(config::validate)
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Base URL is required");
    }

    @Test
    void shouldThrowExceptionWhenUsernameIsMissing() {
        // Given
        ConfluenceConfig config = new ConfluenceConfig();
        config.setBaseUrl("https://confluence.example.com");
        config.setApiToken("api-token");
        config.setSpaceKey("SPACE");
        config.setPageTitle("Test Page");

        // When & Then
        assertThatThrownBy(config::validate)
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Username is required");
    }

    @Test
    void shouldThrowExceptionWhenApiTokenIsMissing() {
        // Given
        ConfluenceConfig config = new ConfluenceConfig();
        config.setBaseUrl("https://confluence.example.com");
        config.setUsername("<EMAIL>");
        config.setSpaceKey("SPACE");
        config.setPageTitle("Test Page");

        // When & Then
        assertThatThrownBy(config::validate)
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("API Token is required");
    }

    @Test
    void shouldThrowExceptionWhenSpaceKeyIsMissing() {
        // Given
        ConfluenceConfig config = new ConfluenceConfig();
        config.setBaseUrl("https://confluence.example.com");
        config.setUsername("<EMAIL>");
        config.setApiToken("api-token");
        config.setPageTitle("Test Page");

        // When & Then
        assertThatThrownBy(config::validate)
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Space Key is required");
    }

    @Test
    void shouldThrowExceptionWhenPageTitleIsMissing() {
        // Given
        ConfluenceConfig config = new ConfluenceConfig();
        config.setBaseUrl("https://confluence.example.com");
        config.setUsername("<EMAIL>");
        config.setApiToken("api-token");
        config.setSpaceKey("SPACE");

        // When & Then
        assertThatThrownBy(config::validate)
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Page Title is required");
    }

    @Test
    void shouldHaveToStringMethod() {
        // Given
        ConfluenceConfig config = new ConfluenceConfig(
                "https://confluence.example.com",
                "<EMAIL>",
                "api-token",
                "SPACE"
        );
        config.setPageTitle("Test Page");

        // When
        String toString = config.toString();

        // Then
        assertThat(toString).contains("ConfluenceConfig");
        assertThat(toString).contains("baseUrl='https://confluence.example.com'");
        assertThat(toString).contains("username='<EMAIL>'");
        assertThat(toString).contains("spaceKey='SPACE'");
        assertThat(toString).contains("pageTitle='Test Page'");
    }
}
