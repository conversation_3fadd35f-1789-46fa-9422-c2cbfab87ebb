package com.library.cucumber.report;

import com.library.model.CucumberReport;
import com.library.model.ScenarioElement;
import com.library.model.Step;
import com.library.model.StepResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class CucumberReportGeneratorTest {

    private CucumberReportGenerator generator;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        generator = new CucumberReportGenerator();
    }

    @Test
    void shouldParseCucumberJsonSuccessfully() throws IOException {
        // Given
        String jsonContent = """
            [
              {
                "uri": "features/login.feature",
                "id": "login-feature",
                "keyword": "Feature",
                "name": "Login Feature",
                "description": "Test login functionality",
                "line": 1,
                "elements": [
                  {
                    "id": "login-feature;successful-login",
                    "keyword": "Scenario",
                    "name": "Successful login",
                    "line": 3,
                    "type": "scenario",
                    "steps": [
                      {
                        "keyword": "Given ",
                        "name": "user is on login page",
                        "line": 4,
                        "result": {
                          "status": "passed",
                          "duration": 1000000000
                        }
                      },
                      {
                        "keyword": "When ",
                        "name": "user enters valid credentials",
                        "line": 5,
                        "result": {
                          "status": "passed",
                          "duration": 2000000000
                        }
                      },
                      {
                        "keyword": "Then ",
                        "name": "user should be logged in",
                        "line": 6,
                        "result": {
                          "status": "passed",
                          "duration": 500000000
                        }
                      }
                    ]
                  }
                ]
              }
            ]
            """;

        File jsonFile = tempDir.resolve("cucumber.json").toFile();
        try (FileWriter writer = new FileWriter(jsonFile)) {
            writer.write(jsonContent);
        }

        // When
        List<CucumberReport> reports = generator.parseCucumberJson(jsonFile.getAbsolutePath());

        // Then
        assertThat(reports).hasSize(1);
        CucumberReport report = reports.get(0);
        assertThat(report.getName()).isEqualTo("Login Feature");
        assertThat(report.getUri()).isEqualTo("features/login.feature");
        assertThat(report.getTotalScenarios()).isEqualTo(1);
        assertThat(report.getPassedScenarios()).isEqualTo(1);
        assertThat(report.getFailedScenarios()).isEqualTo(0);
    }

    @Test
    void shouldThrowExceptionWhenJsonFileNotFound() {
        // Given
        String nonExistentPath = "/path/to/nonexistent/file.json";

        // When & Then
        assertThatThrownBy(() -> generator.parseCucumberJson(nonExistentPath))
                .isInstanceOf(IOException.class)
                .hasMessageContaining("Cucumber JSON file not found");
    }

    @Test
    void shouldGenerateHtmlReportSuccessfully() {
        // Given
        CucumberReport report = createSampleReport();
        List<CucumberReport> reports = Arrays.asList(report);

        // When
        String htmlReport = generator.generateHtmlReport(reports);

        // Then
        assertThat(htmlReport).isNotNull();
        assertThat(htmlReport).contains("<!DOCTYPE html>");
        assertThat(htmlReport).contains("Cucumber Test Report");
        assertThat(htmlReport).contains("Sample Feature");
        assertThat(htmlReport).contains("Sample Scenario");
        assertThat(htmlReport).contains("Given user is on page");
        assertThat(htmlReport).contains("</html>");
    }

    @Test
    void shouldCalculateStatisticsCorrectly() {
        // Given
        CucumberReport report1 = createSampleReport();
        CucumberReport report2 = createFailedReport();
        List<CucumberReport> reports = Arrays.asList(report1, report2);

        // When
        String htmlReport = generator.generateHtmlReport(reports);

        // Then
        assertThat(htmlReport).contains("Features</h3>");
        assertThat(htmlReport).contains("Total Scenarios</h3>");
        assertThat(htmlReport).contains("Passed</h3>");
        assertThat(htmlReport).contains("Failed</h3>");
        assertThat(htmlReport).contains("Success Rate</h3>");
    }

    private CucumberReport createSampleReport() {
        CucumberReport report = new CucumberReport();
        report.setUri("features/sample.feature");
        report.setId("sample-feature");
        report.setName("Sample Feature");
        report.setDescription("Sample feature description");

        ScenarioElement scenario = new ScenarioElement();
        scenario.setId("sample-scenario");
        scenario.setKeyword("Scenario");
        scenario.setName("Sample Scenario");
        scenario.setType("scenario");

        Step step1 = new Step();
        step1.setKeyword("Given ");
        step1.setName("user is on page");
        StepResult result1 = new StepResult();
        result1.setStatus("passed");
        result1.setDuration(1000000000L);
        step1.setResult(result1);

        Step step2 = new Step();
        step2.setKeyword("When ");
        step2.setName("user performs action");
        StepResult result2 = new StepResult();
        result2.setStatus("passed");
        result2.setDuration(2000000000L);
        step2.setResult(result2);

        scenario.setSteps(Arrays.asList(step1, step2));
        report.setElements(Arrays.asList(scenario));

        return report;
    }

    private CucumberReport createFailedReport() {
        CucumberReport report = new CucumberReport();
        report.setUri("features/failed.feature");
        report.setId("failed-feature");
        report.setName("Failed Feature");

        ScenarioElement scenario = new ScenarioElement();
        scenario.setId("failed-scenario");
        scenario.setKeyword("Scenario");
        scenario.setName("Failed Scenario");
        scenario.setType("scenario");

        Step step1 = new Step();
        step1.setKeyword("Given ");
        step1.setName("user is on page");
        StepResult result1 = new StepResult();
        result1.setStatus("passed");
        result1.setDuration(1000000000L);
        step1.setResult(result1);

        Step step2 = new Step();
        step2.setKeyword("When ");
        step2.setName("user performs action");
        StepResult result2 = new StepResult();
        result2.setStatus("failed");
        result2.setDuration(500000000L);
        result2.setErrorMessage("Assertion failed");
        step2.setResult(result2);

        scenario.setSteps(Arrays.asList(step1, step2));
        report.setElements(Arrays.asList(scenario));

        return report;
    }
}
