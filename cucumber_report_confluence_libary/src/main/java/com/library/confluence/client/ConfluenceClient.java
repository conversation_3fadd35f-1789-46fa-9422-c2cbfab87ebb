package com.library.confluence.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.library.config.ConfluenceConfig;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.classic.methods.HttpPut;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * Client for interacting with Confluence REST API
 */
public class ConfluenceClient {
    private static final Logger logger = LoggerFactory.getLogger(ConfluenceClient.class);
    
    private final ConfluenceConfig config;
    private final CloseableHttpClient httpClient;
    private final ObjectMapper objectMapper;
    public ConfluenceClient(ConfluenceConfig config) {
        this.config = config;

            config.validate();
        
        this.httpClient = HttpClients.createDefault();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Create or update a page with the given content
     */
    public String createOrUpdatePage(String title, String content) throws IOException {
        logger.info("Creating or updating page: {}", title);
        
        // First, try to find existing page
        String pageId = findPageByTitle(title);
        
        if (pageId != null) {
            logger.info("Page found with ID: {}, updating...", pageId);
            return updatePage(pageId, title, content);
        } else if (config.isCreatePageIfNotExists()) {
            logger.info("Page not found, creating new page...");
            return createPage(title, content);
        } else {
            throw new IOException("Page not found and createPageIfNotExists is false");
        }
    }

    /**
     * Find page by title in the configured space
     */
    private String findPageByTitle(String title) throws IOException {
        String baseApiUrl =
            config.getBaseUrl();
        // URL encode the parameters to handle spaces and special characters
        String encodedSpaceKey = URLEncoder.encode(config.getSpaceKey(), StandardCharsets.UTF_8);
        String encodedTitle = URLEncoder.encode(title, StandardCharsets.UTF_8);

        String url = String.format("%s/rest/api/content?spaceKey=%s&title=%s&expand=version",
                baseApiUrl, encodedSpaceKey, encodedTitle);
        
        HttpGet request = new HttpGet(url);
        request.setHeader("Authorization", createAuthHeader(config));
        request.setHeader("Accept", "application/json");

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            String responseBody = new String(response.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8);
            
            if (response.getCode() == 200) {
                JsonNode jsonResponse = objectMapper.readTree(responseBody);
                JsonNode results = jsonResponse.get("results");
                
                if (results.isArray() && results.size() > 0) {
                    return results.get(0).get("id").asText();
                }
            } else {
                logger.warn("Failed to search for page. Status: {}, Response: {}", response.getCode(), responseBody);
            }
        }
        
        return null;
    }

    /**
     * Create a new page
     */
    private String createPage(String title, String content) throws IOException {
        String url = config.getBaseUrl() + "/rest/api/content";

        String requestBody = createPageRequestBody(title, content, null);

        HttpPost request = new HttpPost(url);
        request.setHeader("Authorization", createAuthHeader(config));  // Fixed line
        request.setHeader("Content-Type", "application/json");
        request.setEntity(new StringEntity(requestBody, ContentType.APPLICATION_JSON));

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            String responseBody = new String(response.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8);

            if (response.getCode() == 200 || response.getCode() == 201) {
                JsonNode jsonResponse = objectMapper.readTree(responseBody);
                String pageId = jsonResponse.get("id").asText();
                String pageUrl = jsonResponse.get("_links").get("webui").asText();

                logger.info("Page created successfully. ID: {}, URL: {}{}", pageId, config.getBaseUrl(), pageUrl);
                return pageId;
            } else {
                throw new IOException("Failed to create page. Status: " + response.getCode() + ", Response: " + responseBody);
            }
        }
    }

    private String createAuthHeader(ConfluenceConfig config) {
        return config.getTypeOfToken() + " " + config.getApiToken();
    }


    /**
     * Update an existing page
     */
    private String updatePage(String pageId, String title, String content) throws IOException {
        // First get current version
        int currentVersion = getCurrentPageVersion(pageId);
        
        String url = config.getBaseUrl() + "/rest/api/content/" + pageId;
        String requestBody = createPageRequestBody(title, content, currentVersion + 1);
        
        HttpPut request = new HttpPut(url);
        request.setHeader("Authorization", createAuthHeader(config));
        request.setHeader("Content-Type", "application/json");
        request.setEntity(new StringEntity(requestBody, ContentType.APPLICATION_JSON));

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            String responseBody = new String(response.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8);
            
            if (response.getCode() == 200) {
                JsonNode jsonResponse = objectMapper.readTree(responseBody);
                String pageUrl = jsonResponse.get("_links").get("webui").asText();
                
                logger.info("Page updated successfully. ID: {}, URL: {}{}", pageId, config.getBaseUrl(), pageUrl);
                return pageId;
            } else {
                throw new IOException("Failed to update page. Status: " + response.getCode() + ", Response: " + responseBody);
            }
        }
    }

    /**
     * Get current version of a page
     */
    private int getCurrentPageVersion(String pageId) throws IOException {
        String url = config.getBaseUrl() + "/rest/api/content/" + pageId + "?expand=version";
        
        HttpGet request = new HttpGet(url);
        request.setHeader("Authorization", createAuthHeader(config));
        request.setHeader("Accept", "application/json");

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            String responseBody = new String(response.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8);
            
            if (response.getCode() == 200) {
                JsonNode jsonResponse = objectMapper.readTree(responseBody);
                return jsonResponse.get("version").get("number").asInt();
            } else {
                throw new IOException("Failed to get page version. Status: " + response.getCode() + ", Response: " + responseBody);
            }
        }
    }

    private String createPageRequestBody(String title, String content, Integer version) {
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"type\": \"page\",");
        json.append("\"title\": \"").append(escapeJson(title)).append("\",");
        json.append("\"space\": {\"key\": \"").append(config.getSpaceKey()).append("\"},");
        
        if (config.getParentPageId() != null) {
            json.append("\"ancestors\": [{\"id\": \"").append(config.getParentPageId()).append("\"}],");
        }
        
        if (version != null) {
            json.append("\"version\": {\"number\": ").append(version).append("},");
        }
        
        json.append("\"body\": {");
        json.append("\"storage\": {");
        json.append("\"value\": \"").append(escapeJson(content)).append("\",");
        json.append("\"representation\": \"storage\"");
        json.append("}");
        json.append("}");
        json.append("}");
        
        return json.toString();
    }

    private String escapeJson(String text) {
        if (text == null) return "";
        return text.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }

    /**
     * Close the HTTP client
     */
    public void close() throws IOException {
        if (httpClient != null) {
            httpClient.close();
        }
    }
}
