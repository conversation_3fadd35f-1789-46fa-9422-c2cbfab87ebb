package com.library.cucumber.report;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.library.model.CucumberReport;
import com.library.model.ScenarioElement;
import com.library.model.Step;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Generator for creating HTML reports from Cucumber JSON results
 */
public class CucumberReportGenerator {
    private static final Logger logger = LoggerFactory.getLogger(CucumberReportGenerator.class);
    private final ObjectMapper objectMapper;

    public CucumberReportGenerator() {
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Parse Cucumber JSON report file
     */
    public List<CucumberReport> parseCucumberJson(String jsonFilePath) throws IOException {
        logger.info("Parsing Cucumber JSON report from: {}", jsonFilePath);

        File jsonFile = new File(jsonFilePath);
        if (!jsonFile.exists()) {
            throw new IOException("Cucumber JSON file not found: " + jsonFilePath);
        }

        TypeReference<List<CucumberReport>> typeRef = new TypeReference<List<CucumberReport>>() {};
        List<CucumberReport> reports = objectMapper.readValue(jsonFile, typeRef);

        logger.info("Successfully parsed {} feature(s) from JSON report", reports.size());
        return reports;
    }


    /**
     * Generate HTML report from Cucumber reports
     */
    public String generateHtmlReport(List<CucumberReport> reports) {
        logger.info("Generating HTML report for {} feature(s)", reports.size());

        StringBuilder html = new StringBuilder();

        // HTML Header
        html.append(generateHtmlHeader());

        // Summary Section
        html.append(generateSummarySection(reports));

        // Features Section
        html.append(generateFeaturesSection(reports));

        // HTML Footer
        html.append(generateHtmlFooter());

        logger.info("HTML report generated successfully");
        return html.toString();
    }


    private String generateHtmlHeader() {
        return """
                    <h1>Cucumber Test Report</h1>
                    <p><em>Generated on: "" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + ""</em></p>
                """;
    }

    public int getUniqueScenarioCount(List<CucumberReport> reports) {
        return (int) reports.stream()
                .flatMap(report -> report.getElements().stream())
                .map(element -> element.getFileUri() + ":" + element.getLine())
                .distinct()
                .count();
    }

    public int getScenarioOutlineCount(List<CucumberReport> reports) {
        return (int) reports.stream()
                .flatMap(report -> report.getElements().stream())
                .filter(ScenarioElement::isScenarioOutline)
                .map(element -> element.getFileUri() + ":" + element.getLine())
                .distinct()
                .count();
    }

    public int getRegularScenarioCount(List<CucumberReport> reports) {
        return (int) reports.stream()
                .flatMap(report -> report.getElements().stream())
                .filter(element -> !element.isScenarioOutline())
                .map(element -> element.getFileUri() + ":" + element.getLine())
                .distinct()
                .count();
    }

    public int getTotalRunnableScenarioCount(List<CucumberReport> reports) {
        int regularScenarios = getRegularScenarioCount(reports);

        int scenariosFromOutlines = reports.stream()
                .flatMap(report -> report.getElements().stream())
                .filter(ScenarioElement::isScenarioOutline)
                .mapToInt(ScenarioElement::getExampleCount)
                .sum();

        return regularScenarios + scenariosFromOutlines;
    }

    private String generateSummarySection(List<CucumberReport> reports) {
        int totalFeatures = reports.size();
        int totalUniqueScenarios = getUniqueScenarioCount(reports);
        int scenarioOutlines = getScenarioOutlineCount(reports);
        int regularScenarios = getRegularScenarioCount(reports);
        int totalRunnableScenarios = getTotalRunnableScenarioCount(reports);
        int passedScenarios = reports.stream().mapToInt(CucumberReport::getPassedScenarios).sum();
        int failedScenarios = reports.stream().mapToInt(CucumberReport::getFailedScenarios).sum();

        double successRate = totalRunnableScenarios > 0 ? (double) passedScenarios / totalRunnableScenarios * 100 : 0;

        return String.format("""
            <h2>Test Summary</h2>
            <table>
                <tr><th>Metric</th><th>Count</th></tr>
                <tr><td><strong>Features</strong></td><td>%d</td></tr>
                <tr><td><strong>Unique Scenarios</strong></td><td>%d</td></tr>
                <tr><td><strong>Regular Scenarios</strong></td><td>%d</td></tr>
                <tr><td><strong>Scenario Outlines</strong></td><td>%d</td></tr>
                <tr><td><strong>Total Runnable Scenarios</strong></td><td>%d</td></tr>
                <tr><td><strong>Passed Scenarios</strong></td><td style="color: green;">%d</td></tr>
                <tr><td><strong>Failed Scenarios</strong></td><td style="color: red;">%d</td></tr>
                <tr><td><strong>Success Rate</strong></td><td><strong>%.1f%%</strong></td></tr>
            </table>
            """, totalFeatures, totalUniqueScenarios, regularScenarios, scenarioOutlines, totalRunnableScenarios, passedScenarios, failedScenarios, successRate);
    }


    private String generateFeaturesSection(List<CucumberReport> reports) {
        StringBuilder html = new StringBuilder();
        html.append("<h2>🎯 Features</h2>\n");

        for (CucumberReport report : reports) {
            html.append(generateFeatureSection(report));
        }

        return html.toString();
    }

    private String generateFeatureSection(CucumberReport report) {
        StringBuilder html = new StringBuilder();

        html.append(String.format("""
            <h3>%s</h3>
            <p>%s</p>
            <p><strong>Scenarios:</strong> %d | <strong style="color: green;">Passed:</strong> %d | <strong style="color: red;">Failed:</strong> %d | <strong>Success Rate:</strong> %.1f%%</p>
            """,
                report.getName() != null ? report.getName() : "Unknown Feature",
                report.getDescription() != null ? report.getDescription() : "",
                report.getTotalScenarios(),
                report.getPassedScenarios(),
                report.getFailedScenarios(),
                report.getSuccessRate()
        ));

        if (report.getElements() != null) {
            for (ScenarioElement scenario : report.getElements()) {
                html.append(generateScenarioSection(scenario));
            }
        }

        html.append("</div></div>\n");
        return html.toString();
    }

    private String generateScenarioSection(ScenarioElement scenario) {
        String status = scenario.isPassed() ? "passed" : "failed";
        String scenarioType = scenario.isScenarioOutline() ? "Scenario Outline" : "Scenario";

        StringBuilder html = new StringBuilder();
        html.append(String.format("""
            <div class="scenario %s">
                <h3>%s %s</h3>
                <p>%s | Steps: %d | Passed: %d | Failed: %d | Skipped: %d</p>
                <div class="steps">
            """,
                status,
                scenario.getKeyword() != null ? scenario.getKeyword() : "",
                scenario.getName() != null ? scenario.getName() : "Unknown Scenario",
                scenarioType,
                scenario.getTotalSteps(),
                scenario.getPassedSteps(),
                scenario.getFailedSteps(),
                scenario.getSkippedSteps()
        ));

        if (scenario.getSteps() != null) {
            for (Step step : scenario.getSteps()) {
                html.append(generateStepSection(step));
            }
        }

        html.append("</div></div>\n");
        return html.toString();
    }

    private String generateStepSection(Step step) {
        String status = step.getResult() != null ? step.getResult().getStatus() : "unknown";
        String duration = step.getResult() != null ?
                String.format("(%.3fs)", step.getResult().getDurationInSeconds()) : "";

        return String.format("""
            <div class="step %s">
                %s %s
            </div>
            """,
                status,
                step.getFullText(),
                duration
        );
    }

    private String generateHtmlFooter() {
        return """
            <hr/>
            <p><em>Report generated by Cucumber Report Confluence Library</em></p>
            """;
    }

}
