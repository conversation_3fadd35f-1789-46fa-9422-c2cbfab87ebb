package com.library;

import com.library.config.ConfluenceConfig;
import com.library.confluence.client.ConfluenceClient;
import com.library.cucumber.report.CucumberReportGenerator;
import com.library.model.CucumberReport;
import com.library.model.ScenarioElement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Main integration class for generating Cucumber reports and publishing to Confluence
 */
public class CucumberReportConfluenceIntegration {
    private static final Logger logger = LoggerFactory.getLogger(CucumberReportConfluenceIntegration.class);
    
    private final CucumberReportGenerator reportGenerator;
    private ConfluenceClient confluenceClient;
    private ConfluenceConfig confluenceConfig;

    public CucumberReportConfluenceIntegration() {
        this.reportGenerator = new CucumberReportGenerator();
    }

    /**
     * Configure Confluence settings
     */
    public CucumberReportConfluenceIntegration withConfluenceConfig(ConfluenceConfig config) {
        this.confluenceConfig = config;
        this.confluenceClient = new ConfluenceClient(config);
        return this;
    }

    /**
     * Generate HTML report from Cucumber JSON and publish to Confluence
     */
    public String generateAndPublishReport(String cucumberJsonPath) throws IOException {
        return generateAndPublishReport(cucumberJsonPath, null);
    }

    /**
     * Generate HTML report from Cucumber JSON and publish to Confluence with custom title
     */
    public String generateAndPublishReport(String cucumberJsonPath, String customTitle) throws IOException {
        logger.info("Starting Cucumber report generation and Confluence publishing process");
        
        if (confluenceConfig == null || confluenceClient == null) {
            throw new IllegalStateException("Confluence configuration is required. Use withConfluenceConfig() method.");
        }

        try {
            // Parse Cucumber JSON
            List<CucumberReport> reports = reportGenerator.parseCucumberJson(cucumberJsonPath);

            // Generate beautiful Confluence content directly
            String confluenceContent = generateBeautifulConfluenceContent(reports);
            
            // Determine page title
            String pageTitle = customTitle != null ? customTitle : confluenceConfig.getPageTitle();
            if (pageTitle == null) {
                pageTitle = generateDefaultTitle(reports);
            }
            
            // Publish to Confluence
            String pageId = confluenceClient.createOrUpdatePage(pageTitle, confluenceContent);
            
            logger.info("Report successfully published to Confluence. Page ID: {}", pageId);
            return pageId;
            
        } catch (Exception e) {
            logger.error("Failed to generate and publish report", e);
            throw e;
        }
    }

    /**
     * Generate only HTML report without publishing to Confluence
     */
    public String generateHtmlReport(String cucumberJsonPath) throws IOException {
        logger.info("Generating HTML report from Cucumber JSON: {}", cucumberJsonPath);
        
        List<CucumberReport> reports = reportGenerator.parseCucumberJson(cucumberJsonPath);
        return reportGenerator.generateHtmlReport(reports);
    }

    /**
     * Publish existing HTML content to Confluence
     */
    public int getUniqueScenarioCount(List<CucumberReport> reports) {
        Set<String> uniqueNames = new HashSet<>();

        for (CucumberReport report : reports) {
            List<ScenarioElement> elements = report.getElements();
            if (elements != null) {
                for (ScenarioElement element : elements) {
                    if (element.getName() != null) {
                        uniqueNames.add(element.getName());
                    }
                }
            }
        }

        return uniqueNames.size();
    }

    public String publishHtmlToConfluence(String htmlContent, String pageTitle) throws IOException {
        logger.info("Publishing HTML content to Confluence page: {}", pageTitle);
        
        if (confluenceConfig == null || confluenceClient == null) {
            throw new IllegalStateException("Confluence configuration is required. Use withConfluenceConfig() method.");
        }

        String confluenceContent = convertHtmlToConfluenceStorage(htmlContent);
        return confluenceClient.createOrUpdatePage(pageTitle, confluenceContent);
    }

    /**
     * Convert HTML to Confluence storage format with beautiful macros
     */
    private String convertHtmlToConfluenceStorage(String htmlContent) {
        // Instead of converting HTML, create beautiful Confluence content directly
        // Parse the reports again to create native Confluence content
        return htmlContent; // We'll generate Confluence content directly in the generator
    }

    /**
     * Generate beautiful Confluence content directly
     */
    public String generateConfluenceReport(String cucumberJsonPath) throws IOException {
        logger.info("Generating beautiful Confluence report from Cucumber JSON: {}", cucumberJsonPath);

        List<CucumberReport> reports = reportGenerator.parseCucumberJson(cucumberJsonPath);
        return generateBeautifulConfluenceContent(reports);
    }

    /**
     * Generate beautiful Confluence content with macros and formatting
     */
    private String generateBeautifulConfluenceContent(List<CucumberReport> reports) {
        StringBuilder content = new StringBuilder();

        content.append("<ac:structured-macro ac:name=\"info\">");
        content.append("<ac:parameter ac:name=\"icon\">true</ac:parameter>");
        content.append("<ac:parameter ac:name=\"title\">Cucumber Test Report</ac:parameter>");
        content.append("<ac:rich-text-body>");
        content.append("<p>Generated on: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("</p>");
        content.append("</ac:rich-text-body>");
        content.append("</ac:structured-macro>");

        // Summary statistics with status macros
        content.append(generateConfluenceSummary(reports));

        // Features with expand macros
        content.append(generateConfluenceFeatures(reports));

        // Footer with note macro
        content.append("<ac:structured-macro ac:name=\"note\">");
        content.append("<ac:parameter ac:name=\"title\">Report Information</ac:parameter>");
        content.append("<ac:rich-text-body>");
        content.append("<p>This report was automatically generated by <strong>Cucumber Report Confluence Library</strong></p>");
        content.append("<p>Last updated: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("</p>");
        content.append("<p>Total features analyzed: ").append(reports.size()).append("</p>");
        content.append("</ac:rich-text-body>");
        content.append("</ac:structured-macro>");

        return content.toString();
    }

    /**
     * Generate beautiful summary section with status macros
     */
    private String generateConfluenceSummary(List<CucumberReport> reports) {
        int totalFeatures = reports.size();
        int totalScenarios = getUniqueScenarioCount(reports);
        int passedScenarios = reports.stream().mapToInt(CucumberReport::getPassedScenarios).sum();
        int failedScenarios = reports.stream().mapToInt(CucumberReport::getFailedScenarios).sum();
        double successRate = totalScenarios > 0 ? (double) passedScenarios / totalScenarios * 100 : 0;

        StringBuilder summary = new StringBuilder();

        // Summary table with status macros
        summary.append("<h2>Test Summary</h2>");
        summary.append("<table>");
        summary.append("<tbody>");
        summary.append("<tr><th>Metric</th><th>Value</th><th>Status</th></tr>");

        summary.append("<tr><td><strong>Features</strong></td><td>").append(totalFeatures).append("</td>");
        summary.append("<td><ac:structured-macro ac:name=\"status\"><ac:parameter ac:name=\"colour\">Blue</ac:parameter><ac:parameter ac:name=\"title\">").append(totalFeatures).append(" Features</ac:parameter></ac:structured-macro></td></tr>");

        summary.append("<tr><td><strong>Total Scenarios</strong></td><td>").append(totalScenarios).append("</td>");
        summary.append("<td><ac:structured-macro ac:name=\"status\"><ac:parameter ac:name=\"colour\">Grey</ac:parameter><ac:parameter ac:name=\"title\">").append(totalScenarios).append(" Total</ac:parameter></ac:structured-macro></td></tr>");

        summary.append("<tr><td><strong>Passed Scenarios</strong></td><td>").append(passedScenarios).append("</td>");
        summary.append("<td><ac:structured-macro ac:name=\"status\"><ac:parameter ac:name=\"colour\">Green</ac:parameter><ac:parameter ac:name=\"title\">").append(passedScenarios).append(" Passed</ac:parameter></ac:structured-macro></td></tr>");

        summary.append("<tr><td><strong>Failed Scenarios</strong></td><td>").append(failedScenarios).append("</td>");
        if (failedScenarios > 0) {
            summary.append("<td><ac:structured-macro ac:name=\"status\"><ac:parameter ac:name=\"colour\">Red</ac:parameter><ac:parameter ac:name=\"title\">").append(failedScenarios).append(" Failed</ac:parameter></ac:structured-macro></td></tr>");
        } else {
            summary.append("<td><ac:structured-macro ac:name=\"status\"><ac:parameter ac:name=\"colour\">Green</ac:parameter><ac:parameter ac:name=\"title\">No Failures</ac:parameter></ac:structured-macro></td></tr>");
        }

        summary.append("<tr><td><strong>Success Rate</strong></td><td>").append(String.format("%.1f%%", successRate)).append("</td>");
        if (successRate >= 90) {
            summary.append("<td><ac:structured-macro ac:name=\"status\"><ac:parameter ac:name=\"colour\">Green</ac:parameter><ac:parameter ac:name=\"title\">Excellent</ac:parameter></ac:structured-macro></td></tr>");
        } else if (successRate >= 70) {
            summary.append("<td><ac:structured-macro ac:name=\"status\"><ac:parameter ac:name=\"colour\">Yellow</ac:parameter><ac:parameter ac:name=\"title\">Good</ac:parameter></ac:structured-macro></td></tr>");
        } else {
            summary.append("<td><ac:structured-macro ac:name=\"status\"><ac:parameter ac:name=\"colour\">Red</ac:parameter><ac:parameter ac:name=\"title\">Needs Improvement</ac:parameter></ac:structured-macro></td></tr>");
        }

        summary.append("</tbody>");
        summary.append("</table>");

        return summary.toString();
    }

    /**
     * Generate beautiful features section with expand macros
     */
    private String generateConfluenceFeatures(List<CucumberReport> reports) {
        StringBuilder features = new StringBuilder();

        features.append("<h2>Feature Details</h2>");

        for (CucumberReport report : reports) {
            String featureName = report.getName() != null ? report.getName() : "Unknown Feature";
            String featureStatus = report.getFailedScenarios() > 0 ? "Red" : "Green";
            String featureStatusText = report.getFailedScenarios() > 0 ? "Has Failures" : "All Passed";

            // Feature header with expand macro
            features.append("<ac:structured-macro ac:name=\"expand\">");
            features.append("<ac:parameter ac:name=\"title\">");
            features.append("Feature: ").append(featureName);
            features.append(" - <ac:structured-macro ac:name=\"status\"><ac:parameter ac:name=\"colour\">").append(featureStatus).append("</ac:parameter><ac:parameter ac:name=\"title\">").append(featureStatusText).append("</ac:parameter></ac:structured-macro>");
            features.append("</ac:parameter>");
            features.append("<ac:rich-text-body>");

            // Feature description
            if (report.getDescription() != null && !report.getDescription().trim().isEmpty()) {
                features.append("<p><em>").append(report.getDescription()).append("</em></p>");
            }

            // Feature statistics
            features.append("<p><strong>Statistics:</strong> ");
            features.append("Total: ").append(report.getTotalScenarios()).append(" | ");
            features.append("Passed: ").append(report.getPassedScenarios()).append(" | ");
            features.append("Failed: ").append(report.getFailedScenarios()).append(" | ");
            features.append("Success Rate: ").append(String.format("%.1f%%", report.getSuccessRate()));
            features.append("</p>");

            // Scenarios
            if (report.getElements() != null && !report.getElements().isEmpty()) {
                features.append("<h4>Scenarios:</h4>");
                features.append("<ul>");

                for (ScenarioElement scenario : report.getElements()) {
                    String scenarioName = scenario.getName() != null ? scenario.getName() : "Unknown Scenario";
                    boolean scenarioPassed = scenario.getSteps().stream()
                            .allMatch(step -> "passed".equals(step.getResult().getStatus()));

                    features.append("<li>");
                    if (scenarioPassed) {
                        features.append("<strong>").append(scenarioName).append("</strong> - ");
                        features.append("<ac:structured-macro ac:name=\"status\"><ac:parameter ac:name=\"colour\">Green</ac:parameter><ac:parameter ac:name=\"title\">Passed</ac:parameter></ac:structured-macro>");
                    } else {
                        features.append("<strong>").append(scenarioName).append("</strong> - ");
                        features.append("<ac:structured-macro ac:name=\"status\"><ac:parameter ac:name=\"colour\">Red</ac:parameter><ac:parameter ac:name=\"title\">Failed</ac:parameter></ac:structured-macro>");
                    }
                    features.append("</li>");
                }

                features.append("</ul>");
            }

            features.append("</ac:rich-text-body>");
            features.append("</ac:structured-macro>");
        }

        return features.toString();
    }

    /**
     * Generate default page title based on report content
     */
    private String generateDefaultTitle(List<CucumberReport> reports) {
        int totalScenarios = reports.stream().mapToInt(CucumberReport::getTotalScenarios).sum();
        int passedScenarios = reports.stream().mapToInt(CucumberReport::getPassedScenarios).sum();
        int failedScenarios = reports.stream().mapToInt(CucumberReport::getFailedScenarios).sum();

        return String.format("Cucumber Test Report - %d Features, %d Scenarios (%d Passed, %d Failed)",
                reports.size(), totalScenarios, passedScenarios, failedScenarios);
    }

    /**
     * Get report statistics
     */
    public ReportStatistics getReportStatistics(String cucumberJsonPath) throws IOException {
        List<CucumberReport> reports = reportGenerator.parseCucumberJson(cucumberJsonPath);
        return new ReportStatistics(reports);
    }

    /**
     * Close resources
     */
    public void close() throws IOException {
        if (confluenceClient != null) {
            confluenceClient.close();
        }
    }

    /**
    /
     * Inner class for report statistics
     */
    public static class ReportStatistics {
        private final int totalFeatures;
        private final int totalScenarios;
        private final int passedScenarios;
        private final int failedScenarios;
        private final double successRate;

        public ReportStatistics(List<CucumberReport> reports) {
            this.totalFeatures = reports.size();
            this.totalScenarios =(int) reports.stream()
                    .flatMap(report -> report.getElements().stream())
                    .map(element -> element.getFileUri() + ":" + element.getLine())
                    .distinct()
                    .count();
            this.passedScenarios = reports.stream().mapToInt(CucumberReport::getPassedScenarios).sum();
            this.failedScenarios = reports.stream().mapToInt(CucumberReport::getFailedScenarios).sum();
            this.successRate = totalScenarios > 0 ? (double) passedScenarios / totalScenarios * 100 : 0;
        }

        // Getters
        public int getTotalFeatures() { return totalFeatures; }
        public int getTotalScenarios() { return totalScenarios; }
        public int getPassedScenarios() { return passedScenarios; }
        public int getFailedScenarios() { return failedScenarios; }
        public double getSuccessRate() { return successRate; }

        @Override
        public String toString() {
            return String.format("ReportStatistics{features=%d, scenarios=%d, passed=%d, failed=%d, successRate=%.1f%%}",
                    totalFeatures, totalScenarios, passedScenarios, failedScenarios, successRate);
        }

    }
}
