package com.library.config;

/**
 * Configuration class for Confluence connection settings
 */
public class ConfluenceConfig {
    private String baseUrl;
    private String username;
    private String apiToken;
    private String spaceKey;
    private String pageTitle;
    private String parentPageId;
    private boolean createPageIfNotExists = true;
    private int connectionTimeout = 30000; // 30 seconds
    private int readTimeout = 60000; // 60 seconds
    private String typeOfToken;
    public ConfluenceConfig() {}


    public ConfluenceConfig(String baseUrl, String username, String apiToken, String spaceKey,String typeOfToken) {
        this.baseUrl = baseUrl;
        this.username = username;
        this.apiToken = apiToken;
        this.spaceKey = spaceKey;
        this.typeOfToken = typeOfToken;
    }

    public String getTypeOfToken() {
        return typeOfToken;
    }

    public void setTypeOfToken(String typeOfToken) {
        this.typeOfToken = typeOfToken;
    }

    public String getBaseUrl() {
        return baseUrl;
    }


    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getApiToken() {
        return apiToken;
    }

    public void setApiToken(String apiToken) {
        this.apiToken = apiToken;
    }

    public String getSpaceKey() {
        return spaceKey;
    }

    public void setSpaceKey(String spaceKey) {
        this.spaceKey = spaceKey;
    }

    public String getPageTitle() {
        return pageTitle;
    }

    public void setPageTitle(String pageTitle) {
        this.pageTitle = pageTitle;
    }

    public String getParentPageId() {
        return parentPageId;
    }

    public void setParentPageId(String parentPageId) {
        this.parentPageId = parentPageId;
    }

    public boolean isCreatePageIfNotExists() {
        return createPageIfNotExists;
    }

    public void setCreatePageIfNotExists(boolean createPageIfNotExists) {
        this.createPageIfNotExists = createPageIfNotExists;
    }

    public int getConnectionTimeout() {
        return connectionTimeout;
    }

    public void setConnectionTimeout(int connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    /**
     * Check if OAuth mode is enabled
     * @return true if OAuth credentials are provided, false otherwise
     */

    /**
     * Validates the OAuth configuration
     * @throws IllegalArgumentException if OAuth configuration is invalid
     */
    public void validateOAuth() {
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("Base URL is required");
        }
        if (spaceKey == null || spaceKey.trim().isEmpty()) {
            throw new IllegalArgumentException("Space Key is required");
        }
        if (pageTitle == null || pageTitle.trim().isEmpty()) {
            throw new IllegalArgumentException("Page Title is required");
        }
    }

    /**
     * Validates the configuration
     * @throws IllegalArgumentException if configuration is invalid
     */
    public void validate() {
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("Base URL is required");
        }
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("Username is required");
        }
        if (apiToken == null || apiToken.trim().isEmpty()) {
            throw new IllegalArgumentException("API Token is required");
        }
        if (spaceKey == null || spaceKey.trim().isEmpty()) {
            throw new IllegalArgumentException("Space Key is required");
        }
        if (pageTitle == null || pageTitle.trim().isEmpty()) {
            throw new IllegalArgumentException("Page Title is required");
        }
    }



    @Override
    public String toString() {
        return "ConfluenceConfig{" +
                "baseUrl='" + baseUrl + '\'' +
                ", username='" + username + '\'' +
                ", spaceKey='" + spaceKey + '\'' +
                ", pageTitle='" + pageTitle + '\'' +
                ", parentPageId='" + parentPageId + '\'' +
                ", createPageIfNotExists=" + createPageIfNotExists +
                ", connectionTimeout=" + connectionTimeout +
                ", readTimeout=" + readTimeout +
                '}';
    }
}
