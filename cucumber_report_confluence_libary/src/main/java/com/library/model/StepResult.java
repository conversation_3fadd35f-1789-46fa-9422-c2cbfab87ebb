package com.library.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Model representing a Cucumber step result
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class StepResult {
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("duration")
    private long duration;
    
    @JsonProperty("error_message")
    private String errorMessage;

    // Constructors
    public StepResult() {}

    // Getters and Setters
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * Get duration in seconds
     */
    public double getDurationInSeconds() {
        return duration / 1_000_000_000.0;
    }

    /**
     * Check if this step passed
     */
    public boolean isPassed() {
        return "passed".equalsIgnoreCase(status);
    }

    /**
     * Check if this step failed
     */
    public boolean isFailed() {
        return "failed".equalsIgnoreCase(status);
    }

    /**
     * Check if this step was skipped
     */
    public boolean isSkipped() {
        return "skipped".equalsIgnoreCase(status);
    }

    @Override
    public String toString() {
        return "StepResult{" +
                "status='" + status + '\'' +
                ", duration=" + duration +
                ", durationInSeconds=" + getDurationInSeconds() +
                '}';
    }
}
