package com.library.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Model representing a Cucumber scenario
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScenarioElement {
    @JsonProperty("id")
    private String id;

    @JsonProperty("keyword")
    private String keyword;

    public String getFileUri() {
        return fileUri;
    }

    public void setFileUri(String fileUri) {
        this.fileUri = fileUri;
    }

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;
    @JsonProperty("fileUri")
    private String fileUri;

    @JsonProperty("line")
    private int line;

    @JsonProperty("type")
    private String type;

    @JsonProperty("steps")
    private List<Step> steps;

    @JsonProperty("tags")
    private List<Tag> tags;

    @JsonProperty("examples")
    private List<Example> examples;

    // Constructors
    public ScenarioElement() {}

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getLine() {
        return line;
    }

    public void setLine(int line) {
        this.line = line;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<Step> getSteps() {
        return steps;
    }

    public void setSteps(List<Step> steps) {
        this.steps = steps;
    }

    public List<Tag> getTags() {
        return tags;
    }

    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }

    public List<Example> getExamples() {
        return examples;
    }

    public void setExamples(List<Example> examples) {
        this.examples = examples;
    }

    /**
     * Check if this element is a Scenario Outline
     */
    public boolean isScenarioOutline() {
        return "scenario_outline".equals(type) || hasExamples();
    }

    /**
     * Check if this element has examples (indicating it's a Scenario Outline)
     */
    private boolean hasExamples() {
        return examples != null && !examples.isEmpty();
    }

    /**
     * Get total number of examples in this Scenario Outline
     * Returns 0 if this is not a Scenario Outline
     */
    public int getExampleCount() {
        if (!isScenarioOutline()) {
            return 0;
        }

        return examples.stream()
                .mapToInt(Example::getRowCount)
                .sum();
    }

    /**
     * Check if this scenario passed (all steps passed)
     */
    public boolean isPassed() {
        if (steps == null || steps.isEmpty()) return false;
        return steps.stream().allMatch(step -> "passed".equalsIgnoreCase(step.getResult().getStatus()));
    }

    /**
     * Get total steps
     */
    public int getTotalSteps() {
        return steps != null ? steps.size() : 0;
    }

    /**
     * Get passed steps
     */
    public int getPassedSteps() {
        if (steps == null) return 0;
        return (int) steps.stream()
                .filter(step -> "passed".equalsIgnoreCase(step.getResult().getStatus()))
                .count();
    }

    /**
     * Get failed steps
     */
    public int getFailedSteps() {
        if (steps == null) return 0;
        return (int) steps.stream()
                .filter(step -> "failed".equalsIgnoreCase(step.getResult().getStatus()))
                .count();
    }

    /**
     * Get skipped steps
     */
    public int getSkippedSteps() {
        if (steps == null) return 0;
        return (int) steps.stream()
                .filter(step -> "skipped".equalsIgnoreCase(step.getResult().getStatus()))
                .count();
    }

    @Override
    public String toString() {
        return "ScenarioElement{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                (isScenarioOutline() ? ", scenarioOutline=true, exampleCount=" + getExampleCount() : "") +
                ", totalSteps=" + getTotalSteps() +
                ", passedSteps=" + getPassedSteps() +
                ", failedSteps=" + getFailedSteps() +
                ", skippedSteps=" + getSkippedSteps() +
                '}';
    }

    /**
     * Example model for Scenario Outline examples
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Example {
        @JsonProperty("name")
        private String name;

        @JsonProperty("keyword")
        private String keyword;

        @JsonProperty("rows")
        private List<Row> rows;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getKeyword() {
            return keyword;
        }

        public void setKeyword(String keyword) {
            this.keyword = keyword;
        }

        public List<Row> getRows() {
            return rows;
        }

        public void setRows(List<Row> rows) {
            this.rows = rows;
        }

        /**
         * Get the number of examples (data rows, excluding header)
         */
        public int getRowCount() {
            // First row is header, so we count rows - 1 if there are any rows
            return (rows != null && rows.size() > 1) ? rows.size() - 1 : 0;
        }
    }

    /**
     * Row model for example data
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Row {
        @JsonProperty("cells")
        private List<String> cells;

        public List<String> getCells() {
            return cells;
        }

        public void setCells(List<String> cells) {
            this.cells = cells;
        }
    }
}
