package com.library.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Model representing a Cucumber test report
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CucumberReport {
    @JsonProperty("uri")
    private String uri;
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("keyword")
    private String keyword;
    
    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;
    
    @JsonProperty("line")
    private int line;
    
    @JsonProperty("elements")
    private List<ScenarioElement> elements;
    
    @JsonProperty("tags")
    private List<Tag> tags;

    // Constructors
    public CucumberReport() {}

    // Getters and Setters
    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getLine() {
        return line;
    }

    public void setLine(int line) {
        this.line = line;
    }

    public List<ScenarioElement> getElements() {
        return elements;
    }

    public void setElements(List<ScenarioElement> elements) {
        this.elements = elements;
    }

    public List<Tag> getTags() {
        return tags;
    }

    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }

    /**
     * Calculate total scenarios in this feature
     */
    public int getTotalScenarios() {
        return elements != null ? elements.size() : 0;
    }

    /**
     * Calculate passed scenarios
     */
    public int getPassedScenarios() {
        if (elements == null) return 0;
        return (int) elements.stream()
                .filter(ScenarioElement::isPassed)
                .count();
    }

    /**
     * Calculate failed scenarios
     */
    public int getFailedScenarios() {
        if (elements == null) return 0;
        return (int) elements.stream()
                .filter(element -> !element.isPassed())
                .count();
    }

    /**
     * Get success rate as percentage
     */
    public double getSuccessRate() {
        int total = getTotalScenarios();
        if (total == 0) return 0.0;
        return (double) getPassedScenarios() / total * 100;
    }

    @Override
    public String toString() {
        return "CucumberReport{" +
                "uri='" + uri + '\'' +
                ", id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", totalScenarios=" + getTotalScenarios() +
                ", passedScenarios=" + getPassedScenarios() +
                ", failedScenarios=" + getFailedScenarios() +
                '}';
    }
}
