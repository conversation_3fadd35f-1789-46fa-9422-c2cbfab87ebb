package com.library.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Model representing a Cucumber step
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Step {
    @JsonProperty("keyword")
    private String keyword;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("line")
    private int line;
    
    @JsonProperty("result")
    private StepResult result;

    // Constructors
    public Step() {}

    // Getters and Setters
    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getLine() {
        return line;
    }

    public void setLine(int line) {
        this.line = line;
    }

    public StepResult getResult() {
        return result;
    }

    public void setResult(StepResult result) {
        this.result = result;
    }

    /**
     * Get full step text (keyword + name)
     */
    public String getFullText() {
        return (keyword != null ? keyword : "") + (name != null ? name : "");
    }

    @Override
    public String toString() {
        return "Step{" +
                "keyword='" + keyword + '\'' +
                ", name='" + name + '\'' +
                ", line=" + line +
                ", result=" + result +
                '}';
    }
}
