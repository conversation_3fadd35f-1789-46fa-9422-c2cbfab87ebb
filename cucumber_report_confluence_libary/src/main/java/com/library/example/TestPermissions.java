package com.library.example;

import com.library.config.ConfluenceConfig;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class TestPermissions {
    public static void main(String[] args) throws Exception {
        // Test với API token hiện tại
        String baseUrl = "https://ducpm15112003.atlassian.net/wiki";
        String email = "<EMAIL>";
        String apiToken = "ATATT3xFfGF0xXOKpPom3JEPA2cLtW4JGJriOtJwNUJJu_h2xsfR86D7G5cn_eFhP7lzBB52mAPtuRMoovXxEHVfkMzJMPnt0nSa_Wu4c8b9fH3Lz7dkL7YzloeL5e5CUuUwst7F-nkKLq1uSnb8FvNhSHESnzbCQSZK1xZpvxtmLKb0U_WQh6c=A8D17B73";
        
        // Tạo Basic Auth header
        String credentials = email + ":" + apiToken;
        String encodedCredentials = Base64.getEncoder().encodeToString(credentials.getBytes(StandardCharsets.UTF_8));
        String authHeader = "Bearer " + encodedCredentials;
        
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            
            // 1. Test xem có thể list spaces không
            System.out.println("=== Testing Space Access ===");
            HttpGet spacesRequest = new HttpGet(baseUrl + "/rest/api/space");
            spacesRequest.setHeader("Authorization", authHeader);
            spacesRequest.setHeader("Accept", "application/json");
            
            try (CloseableHttpResponse response = httpClient.execute(spacesRequest)) {
                String responseBody = new String(response.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8);
                System.out.println("Spaces Response Status: " + response.getCode());
                System.out.println("Spaces Response: " + responseBody);
            }
            
            // 2. Test xem có thể access space TEST không
            System.out.println("\n=== Testing Space TEST Access ===");
            HttpGet spaceRequest = new HttpGet(baseUrl + "/rest/api/space/TEST");
            spaceRequest.setHeader("Authorization", authHeader);
            spaceRequest.setHeader("Accept", "application/json");
            
            try (CloseableHttpResponse response = httpClient.execute(spaceRequest)) {
                String responseBody = new String(response.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8);
                System.out.println("Space TEST Response Status: " + response.getCode());
                System.out.println("Space TEST Response: " + responseBody);
            }
            
            // 3. Test user permissions
            System.out.println("\n=== Testing Current User ===");
            HttpGet userRequest = new HttpGet(baseUrl + "/rest/api/user/current");
            userRequest.setHeader("Authorization", authHeader);
            userRequest.setHeader("Accept", "application/json");
            
            try (CloseableHttpResponse response = httpClient.execute(userRequest)) {
                String responseBody = new String(response.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8);
                System.out.println("Current User Response Status: " + response.getCode());
                System.out.println("Current User Response: " + responseBody);
            }
        }
    }
}
