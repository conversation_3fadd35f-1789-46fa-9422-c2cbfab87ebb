package com.library.example;

import com.library.CucumberReportConfluenceIntegration;
import com.library.config.ConfluenceConfig;

public class ApiTokenExample {
    public static void main(String[] args) throws Exception {
        // RECOMMENDED: Use API Token authentication (simpler and more reliable)
        // This is the correct way to use the credentials you have
        
        ConfluenceConfig config = new ConfluenceConfig(
            "https://ducpm15112003.atlassian.net/wiki",  // Confluence Base URL (note /wiki)
            "<EMAIL>",                    // Your Atlassian account email (MUST be exact)
            "ATATT3xFfGF0Jp7NDyQ-JcO2KLnzywNy-3CYed_B82Bo8yQh8277ptXFTIX8ZP2f_ww6MFRhv3Kp5PYcx1kzhNIEqw7evmrqut-Xvn0kIzJFLD6jMOKjuxieyl5SCFNcqcuUzMt_FZTMutGKPuReCsxS_AfpxwFQy4yr9nFzz-ADobbFcrVDJyI=8356AE35",                    // Replace with your NEW API token
            "Tester",
                "Bearer"
        );

        config.setPageTitle("Cucumber_Test");
        
        // Create integration with API Token authentication
        CucumberReportConfluenceIntegration integration = 
            new CucumberReportConfluenceIntegration()
                .withConfluenceConfig(config);
        
        // Generate and publish report
        String pageId = integration.generateAndPublishReport("test.json");
        System.out.println("Report published with API Token. Page ID: " + pageId);
        
        integration.close();
    }
}
