[{"line": 2, "elements": [{"start_timestamp": "2025-07-17T03:21:03.332Z", "before": [{"result": {"duration": 4125000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 6028000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 14, "name": "Fail to initialize step-up session - Invalid request data", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-initialize-step-up-session---invalid-request-data;;2", "after": [{"result": {"duration": 63918000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 5626000, "status": "passed"}, "line": 6, "name": "A user profile does not exist in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aProfileDoesNotExistInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 60009000, "status": "passed"}, "line": 7, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/failCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/failCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 616470000, "status": "passed"}, "line": 8, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 7228000, "status": "passed"}, "line": 9, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 6730000, "status": "passed"}, "line": 10, "name": "The error code should be 2003023 and message should be Profile not found", "match": {"arguments": [{"val": "2003023", "offset": 25}, {"val": "Profile not found", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailInitAPI"}]}, {"start_timestamp": "2025-07-17T03:21:04.159Z", "before": [{"result": {"duration": 497000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 506000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 15, "name": "Fail to initialize step-up session - Invalid request data", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-initialize-step-up-session---invalid-request-data;;3", "after": [{"result": {"duration": 214000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 615000, "status": "passed"}, "line": 6, "name": "The request is missing the identifierId", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theRequestIsMissingTheIdentifierId()"}, "keyword": "Given "}, {"result": {"duration": 3177000, "status": "passed"}, "line": 7, "name": "I build the request body for initializing a step-up session with factor PASSCODE, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/failCase/PASS_CODE_authConfig.json", "match": {"arguments": [{"val": "PASSCODE", "offset": 72}, {"val": " 123", "offset": 90}, {"val": "PROFILE_ID", "offset": 110}, {"val": "/dataShare/failCase/PASS_CODE_authConfig.json", "offset": 138}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 40426000, "status": "passed"}, "line": 8, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 2009000, "status": "passed"}, "line": 9, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 548000, "status": "passed"}, "line": 10, "name": "The error code should be 2003001 and message should be Invalid required fields: identifierId must not be blank", "match": {"arguments": [{"val": "2003001", "offset": 25}, {"val": "Invalid required fields: identifierId must not be blank", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailInitAPI"}]}, {"start_timestamp": "2025-07-17T03:21:04.242Z", "before": [{"result": {"duration": 524000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 536000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 16, "name": "Fail to initialize step-up session - Invalid request data", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-initialize-step-up-session---invalid-request-data;;4", "after": [{"result": {"duration": 16937000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 617000, "status": "passed"}, "line": 6, "name": "The request is missing the flowId", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theRequestIsMissingTheFlowId()"}, "keyword": "Given "}, {"result": {"duration": 3245000, "status": "passed"}, "line": 7, "name": "I build the request body for initializing a step-up session with factor PASSCODE, flow id: , profile type: PROFILE_ID, and authConfig: /dataShare/failCase/PASS_CODE_authConfig.json", "match": {"arguments": [{"val": "PASSCODE", "offset": 72}, {"val": " ", "offset": 90}, {"val": "PROFILE_ID", "offset": 107}, {"val": "/dataShare/failCase/PASS_CODE_authConfig.json", "offset": 135}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 11360000, "status": "passed"}, "line": 8, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 2297000, "status": "passed"}, "line": 9, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 475000, "status": "passed"}, "line": 10, "name": "The error code should be 2003001 and message should be Invalid required fields: flowId must not be blank", "match": {"arguments": [{"val": "2003001", "offset": 25}, {"val": "Invalid required fields: flowId must not be blank", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailInitAPI"}]}, {"start_timestamp": "2025-07-17T03:21:04.308Z", "before": [{"result": {"duration": 659000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 765000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 17, "name": "Fail to initialize step-up session - Invalid request data", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-initialize-step-up-session---invalid-request-data;;5", "after": [{"result": {"duration": 20740000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 820000, "status": "passed"}, "line": 6, "name": "The auth config is invalid", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theAuthConfigIsInvalid()"}, "keyword": "Given "}, {"result": {"duration": 4208000, "status": "passed"}, "line": 7, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: USERNAME, and authConfig: /dataShare/failCase/INVALID_OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "USERNAME", "offset": 105}, {"val": "/dataShare/failCase/INVALID_OTP_authConfig.json", "offset": 131}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 42094000, "status": "passed"}, "line": 8, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 1545000, "status": "passed"}, "line": 9, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 442000, "status": "passed"}, "line": 10, "name": "The error code should be 2003001 and message should be Invalid required fields: authConfig.OTP.dialCode must not be blank", "match": {"arguments": [{"val": "2003001", "offset": 25}, {"val": "Invalid required fields: authConfig.OTP.dialCode must not be blank", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailInitAPI"}]}, {"start_timestamp": "2025-07-17T03:21:04.419Z", "before": [{"result": {"duration": 662000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 649000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 30, "name": "Fail to validate step-up session - Session not found", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-validate-step-up-session---session-not-found;;2", "after": [{"result": {"duration": 61064000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 878000, "status": "passed"}, "line": 22, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 7124000, "status": "passed"}, "line": 23, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 58639000, "status": "passed"}, "line": 24, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: true", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "true", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 2145000, "status": "passed"}, "line": 25, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 530000, "status": "passed"}, "line": 26, "name": "The error code should be 2003017 and message should be Step up session does not exist", "match": {"arguments": [{"val": "2003017", "offset": 25}, {"val": "Step up session does not exist", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailValidation"}]}, {"start_timestamp": "2025-07-17T03:21:04.574Z", "before": [{"result": {"duration": 520000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 483000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 31, "name": "Fail to validate step-up session - Session not found", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-validate-step-up-session---session-not-found;;3", "after": [{"result": {"duration": 56695000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 519000, "status": "passed"}, "line": 22, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 465000, "status": "passed"}, "line": 23, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 70305000, "status": "passed"}, "line": 24, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: false", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "false", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 1539000, "status": "passed"}, "line": 25, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 430000, "status": "passed"}, "line": 26, "name": "The error code should be 2003017 and message should be Step up session does not exist", "match": {"arguments": [{"val": "2003017", "offset": 25}, {"val": "Step up session does not exist", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailValidation"}]}, {"start_timestamp": "2025-07-17T03:21:04.726Z", "before": [{"result": {"duration": 444000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 387000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 32, "name": "Fail to validate step-up session - Session not found", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-validate-step-up-session---session-not-found;;4", "after": [{"result": {"duration": 134420000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 370000, "status": "passed"}, "line": 22, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 334098000, "status": "passed"}, "line": 23, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 61151000, "status": "passed"}, "line": 24, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: true", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "true", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 1613000, "status": "passed"}, "line": 25, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 402000, "status": "passed"}, "line": 26, "name": "The error code should be 2003017 and message should be Step up session does not exist", "match": {"arguments": [{"val": "2003017", "offset": 25}, {"val": "Step up session does not exist", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailValidation"}]}, {"start_timestamp": "2025-07-17T03:21:05.282Z", "before": [{"result": {"duration": 462000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 462000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 33, "name": "Fail to validate step-up session - Session not found", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-validate-step-up-session---session-not-found;;5", "after": [{"result": {"duration": 115717000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 409000, "status": "passed"}, "line": 22, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 125207000, "status": "passed"}, "line": 23, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 58812000, "status": "passed"}, "line": 24, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: false", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "false", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 1204000, "status": "passed"}, "line": 25, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 262000, "status": "passed"}, "line": 26, "name": "The error code should be 2003017 and message should be Step up session does not exist", "match": {"arguments": [{"val": "2003017", "offset": 25}, {"val": "Step up session does not exist", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailValidation"}]}, {"start_timestamp": "2025-07-17T03:21:05.611Z", "before": [{"result": {"duration": 364000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 432000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 48, "name": "Fail to validate step-up session - Invalid factor", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-validate-step-up-session---invalid-factor;;2", "after": [{"result": {"duration": 55178000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 534000, "status": "passed"}, "line": 37, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 542000, "status": "passed"}, "line": 38, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 4881000, "status": "passed"}, "line": 39, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 179051000, "status": "passed"}, "line": 40, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 882000, "status": "passed"}, "line": 41, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 66218000, "status": "passed"}, "line": 42, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: true", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "true", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 1042000, "status": "passed"}, "line": 43, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 262000, "status": "passed"}, "line": 44, "name": "The error code should be 2003011 and message should be The factor provided is not as expected", "match": {"arguments": [{"val": "2003011", "offset": 25}, {"val": "The factor provided is not as expected", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailValidation"}]}, {"start_timestamp": "2025-07-17T03:21:05.942Z", "before": [{"result": {"duration": 437000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 412000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 49, "name": "Fail to validate step-up session - Invalid factor", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-validate-step-up-session---invalid-factor;;3", "after": [{"result": {"duration": 56250000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 830000, "status": "passed"}, "line": 37, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 595000, "status": "passed"}, "line": 38, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2608000, "status": "passed"}, "line": 39, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 76723000, "status": "passed"}, "line": 40, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 1031000, "status": "passed"}, "line": 41, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 63646000, "status": "passed"}, "line": 42, "name": "When the customer go to validation step up with factor: FACIAL and includeFactorConfig: false", "match": {"arguments": [{"val": "FACIAL", "offset": 56}, {"val": "false", "offset": 88}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 1204000, "status": "passed"}, "line": 43, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 344000, "status": "passed"}, "line": 44, "name": "The error code should be 2003011 and message should be The factor provided is not as expected", "match": {"arguments": [{"val": "2003011", "offset": 25}, {"val": "The factor provided is not as expected", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailValidation"}]}, {"start_timestamp": "2025-07-17T03:21:06.166Z", "before": [{"result": {"duration": 300000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 397000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 50, "name": "Fail to validate step-up session - Invalid factor", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-validate-step-up-session---invalid-factor;;4", "after": [{"result": {"duration": 114010000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 366000, "status": "passed"}, "line": 37, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 123266000, "status": "passed"}, "line": 38, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3267000, "status": "passed"}, "line": 39, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 97714000, "status": "passed"}, "line": 40, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 1011000, "status": "passed"}, "line": 41, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 63334000, "status": "passed"}, "line": 42, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: true", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "true", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 905000, "status": "passed"}, "line": 43, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 238000, "status": "passed"}, "line": 44, "name": "The error code should be 2003011 and message should be The factor provided is not as expected", "match": {"arguments": [{"val": "2003011", "offset": 25}, {"val": "The factor provided is not as expected", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailValidation"}]}, {"start_timestamp": "2025-07-17T03:21:06.591Z", "before": [{"result": {"duration": 432000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 520000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 51, "name": "Fail to validate step-up session - Invalid factor", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-validate-step-up-session---invalid-factor;;5", "after": [{"result": {"duration": 110909000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 430000, "status": "passed"}, "line": 37, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 129557000, "status": "passed"}, "line": 38, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1936000, "status": "passed"}, "line": 39, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 86687000, "status": "passed"}, "line": 40, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 1007000, "status": "passed"}, "line": 41, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 62432000, "status": "passed"}, "line": 42, "name": "When the customer go to validation step up with factor: PASSCODE and includeFactorConfig: false", "match": {"arguments": [{"val": "PASSCODE", "offset": 56}, {"val": "false", "offset": 90}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 777000, "status": "passed"}, "line": 43, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 260000, "status": "passed"}, "line": 44, "name": "The error code should be 2003011 and message should be The factor provided is not as expected", "match": {"arguments": [{"val": "2003011", "offset": 25}, {"val": "The factor provided is not as expected", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailValidation"}]}, {"start_timestamp": "2025-07-17T03:21:07.007Z", "before": [{"result": {"duration": 441000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 363000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 67, "name": "Fail to validate step-up session - Session expired", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-validate-step-up-session---session-expired;;2", "after": [{"result": {"duration": 54392000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 371000, "status": "passed"}, "line": 55, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 344000, "status": "passed"}, "line": 56, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2345000, "status": "passed"}, "line": 57, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 102914000, "status": "passed"}, "line": 58, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 888000, "status": "passed"}, "line": 59, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 135870000, "status": "passed"}, "line": 60, "name": "I wait for the session to expire", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.iWaitForTheSessionToExpire()"}, "keyword": "And "}, {"result": {"duration": 70943000, "status": "passed"}, "line": 61, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: true", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "true", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 1028000, "status": "passed"}, "line": 62, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 368000, "status": "passed"}, "line": 63, "name": "The error code should be 2003002 and message should be Auth session expired", "match": {"arguments": [{"val": "2003002", "offset": 25}, {"val": "Auth session expired", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailValidation"}]}, {"start_timestamp": "2025-07-17T03:21:07.399Z", "before": [{"result": {"duration": 477000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 349000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 68, "name": "Fail to validate step-up session - Session expired", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-validate-step-up-session---session-expired;;3", "after": [{"result": {"duration": 112617000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 368000, "status": "passed"}, "line": 55, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 127781000, "status": "passed"}, "line": 56, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3164000, "status": "passed"}, "line": 57, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 88177000, "status": "passed"}, "line": 58, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 938000, "status": "passed"}, "line": 59, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 137513000, "status": "passed"}, "line": 60, "name": "I wait for the session to expire", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.iWaitForTheSessionToExpire()"}, "keyword": "And "}, {"result": {"duration": 63526000, "status": "passed"}, "line": 61, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: false", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "false", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 1233000, "status": "passed"}, "line": 62, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 441000, "status": "passed"}, "line": 63, "name": "The error code should be 2003002 and message should be Auth session expired", "match": {"arguments": [{"val": "2003002", "offset": 25}, {"val": "Auth session expired", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailValidation"}]}, {"start_timestamp": "2025-07-17T03:21:07.957Z", "before": [{"result": {"duration": 326000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 408000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 84, "name": "Fail to validate step-up session - Invalid request parameters", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-validate-step-up-session---invalid-request-parameters;;2", "after": [{"result": {"duration": 55924000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 347000, "status": "passed"}, "line": 72, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 270000, "status": "passed"}, "line": 73, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1774000, "status": "passed"}, "line": 74, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 512528000, "status": "passed"}, "line": 75, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 953000, "status": "passed"}, "line": 76, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 1074000, "status": "passed"}, "line": 77, "name": "I build invalid validation request with missing stepUpAuthId", "match": {"arguments": [{"val": "stepUpAuthId", "offset": 48}], "location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.iBuildInvalidValidationRequestWithMissing(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 8857000, "status": "passed"}, "line": 78, "name": "When the customer go to validation step up with invalid request", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.whenTheCustomerGoToValidationStepUpWithInvalidRequest()"}, "keyword": "And "}, {"result": {"duration": 1165000, "status": "passed"}, "line": 79, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 415000, "status": "passed"}, "line": 80, "name": "The error code should be 2003001 and message should be Invalid required fields: stepUpAuthId must not be blank", "match": {"arguments": [{"val": "2003001", "offset": 25}, {"val": "Invalid required fields: stepUpAuthId must not be blank", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailValidation"}]}, {"start_timestamp": "2025-07-17T03:21:08.557Z", "before": [{"result": {"duration": 303000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 313000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 85, "name": "Fail to validate step-up session - Invalid request parameters", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-validate-step-up-session---invalid-request-parameters;;3", "after": [{"result": {"duration": 51196000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 314000, "status": "passed"}, "line": 72, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 277000, "status": "passed"}, "line": 73, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1459000, "status": "passed"}, "line": 74, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 85291000, "status": "passed"}, "line": 75, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 737000, "status": "passed"}, "line": 76, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 533000, "status": "passed"}, "line": 77, "name": "I build invalid validation request with missing factor", "match": {"arguments": [{"val": "factor", "offset": 48}], "location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.iBuildInvalidValidationRequestWithMissing(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 6054000, "status": "passed"}, "line": 78, "name": "When the customer go to validation step up with invalid request", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.whenTheCustomerGoToValidationStepUpWithInvalidRequest()"}, "keyword": "And "}, {"result": {"duration": 834000, "status": "passed"}, "line": 79, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 304000, "status": "passed"}, "line": 80, "name": "The error code should be 2003001 and message should be Invalid required fields: authFactor must not be null", "match": {"arguments": [{"val": "2003001", "offset": 25}, {"val": "Invalid required fields: authFactor must not be null", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailValidation"}]}, {"start_timestamp": "2025-07-17T03:21:08.724Z", "before": [{"result": {"duration": 485000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 353000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 102, "name": "Fail to update step-up session - Invalid request parameters", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-update-step-up-session---invalid-request-parameters;;2", "after": [{"result": {"duration": 52273000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 365000, "status": "passed"}, "line": 90, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 307000, "status": "passed"}, "line": 91, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1852000, "status": "passed"}, "line": 92, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 91048000, "status": "passed"}, "line": 93, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 1038000, "status": "passed"}, "line": 94, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 2553000, "status": "passed"}, "line": 95, "name": "I build invalid update request with missing stepUpAuthId", "match": {"arguments": [{"val": "stepUpAuthId", "offset": 44}], "location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.iBuildInvalidUpdateRequestWithMissing(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 19407000, "status": "passed"}, "line": 96, "name": "Next customer go to update step up with invalid request", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.nextCustomerGoToUpdateStepUpWithInvalidRequest()"}, "keyword": "And "}, {"result": {"duration": 904000, "status": "passed"}, "line": 97, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 343000, "status": "passed"}, "line": 98, "name": "The error code should be 2003001 and message should be Invalid required fields: stepUpAuthId must not be blank", "match": {"arguments": [{"val": "2003001", "offset": 25}, {"val": "Invalid required fields: stepUpAuthId must not be blank", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:08.917Z", "before": [{"result": {"duration": 356000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 312000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 103, "name": "Fail to update step-up session - Invalid request parameters", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-update-step-up-session---invalid-request-parameters;;3", "after": [{"result": {"duration": 52735000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 431000, "status": "passed"}, "line": 90, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 368000, "status": "passed"}, "line": 91, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2147000, "status": "passed"}, "line": 92, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 90881000, "status": "passed"}, "line": 93, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 753000, "status": "passed"}, "line": 94, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 688000, "status": "passed"}, "line": 95, "name": "I build invalid update request with missing authFactor", "match": {"arguments": [{"val": "authFactor", "offset": 44}], "location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.iBuildInvalidUpdateRequestWithMissing(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 6008000, "status": "passed"}, "line": 96, "name": "Next customer go to update step up with invalid request", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.nextCustomerGoToUpdateStepUpWithInvalidRequest()"}, "keyword": "And "}, {"result": {"duration": 931000, "status": "passed"}, "line": 97, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 288000, "status": "passed"}, "line": 98, "name": "The error code should be 2003001 and message should be Invalid required fields: authFactor must not be null", "match": {"arguments": [{"val": "2003001", "offset": 25}, {"val": "Invalid required fields: authFactor must not be null", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:09.090Z", "before": [{"result": {"duration": 455000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 323000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 104, "name": "Fail to update step-up session - Invalid request parameters", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-update-step-up-session---invalid-request-parameters;;4", "after": [{"result": {"duration": 110645000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 314000, "status": "passed"}, "line": 90, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 116000000, "status": "passed"}, "line": 91, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2035000, "status": "passed"}, "line": 92, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 85120000, "status": "passed"}, "line": 93, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 657000, "status": "passed"}, "line": 94, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 503000, "status": "passed"}, "line": 95, "name": "I build invalid update request with missing attempts", "match": {"arguments": [{"val": "attempts", "offset": 44}], "location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.iBuildInvalidUpdateRequestWithMissing(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 7464000, "status": "passed"}, "line": 96, "name": "Next customer go to update step up with invalid request", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.nextCustomerGoToUpdateStepUpWithInvalidRequest()"}, "keyword": "And "}, {"result": {"duration": 686000, "status": "passed"}, "line": 97, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 266000, "status": "passed"}, "line": 98, "name": "The error code should be 2003001 and message should be Invalid required fields: attempts must be greater than or equal to 1", "match": {"arguments": [{"val": "2003001", "offset": 25}, {"val": "Invalid required fields: attempts must be greater than or equal to 1", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:09.433Z", "before": [{"result": {"duration": 371000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 299000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 105, "name": "Fail to update step-up session - Invalid request parameters", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-update-step-up-session---invalid-request-parameters;;5", "after": [{"result": {"duration": 113869000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 324000, "status": "passed"}, "line": 90, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 114344000, "status": "passed"}, "line": 91, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1639000, "status": "passed"}, "line": 92, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 89815000, "status": "passed"}, "line": 93, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 817000, "status": "passed"}, "line": 94, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 684000, "status": "passed"}, "line": 95, "name": "I build invalid update request with missing maxAttempts", "match": {"arguments": [{"val": "maxAttempts", "offset": 44}], "location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.iBuildInvalidUpdateRequestWithMissing(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 6708000, "status": "passed"}, "line": 96, "name": "Next customer go to update step up with invalid request", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.nextCustomerGoToUpdateStepUpWithInvalidRequest()"}, "keyword": "And "}, {"result": {"duration": 1137000, "status": "passed"}, "line": 97, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 321000, "status": "passed"}, "line": 98, "name": "The error code should be 2003001 and message should be Invalid required fields: maxAttempts must be greater than or equal to 1", "match": {"arguments": [{"val": "2003001", "offset": 25}, {"val": "Invalid required fields: maxAttempts must be greater than or equal to 1", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:09.784Z", "before": [{"result": {"duration": 337000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 308000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 121, "name": "Fail to update step-up session - Factor mismatch", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-update-step-up-session---factor-mismatch;;2", "after": [{"result": {"duration": 51066000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 434000, "status": "passed"}, "line": 109, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 833000, "status": "passed"}, "line": 110, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2518000, "status": "passed"}, "line": 111, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 97383000, "status": "passed"}, "line": 112, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 981000, "status": "passed"}, "line": 113, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 2803000, "status": "passed"}, "line": 114, "name": "I build the request body for updating a step-up session with factor: DEVICE_BIO, status: SUCCESS, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 69}, {"val": "SUCCESS", "offset": 89}, {"val": "1", "offset": 107}, {"val": "1", "offset": 127}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 116761000, "status": "passed"}, "line": 115, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 725000, "status": "passed"}, "line": 116, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 216000, "status": "passed"}, "line": 117, "name": "The error code should be 2003011 and message should be The factor provided is not as expected", "match": {"arguments": [{"val": "2003011", "offset": 25}, {"val": "The factor provided is not as expected", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:10.076Z", "before": [{"result": {"duration": 294000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 270000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 122, "name": "Fail to update step-up session - Factor mismatch", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-update-step-up-session---factor-mismatch;;3", "after": [{"result": {"duration": 51668000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 452000, "status": "passed"}, "line": 109, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 471000, "status": "passed"}, "line": 110, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2445000, "status": "passed"}, "line": 111, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 103867000, "status": "passed"}, "line": 112, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 868000, "status": "passed"}, "line": 113, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 523000, "status": "passed"}, "line": 114, "name": "I build the request body for updating a step-up session with factor: FACIAL, status: FAILED, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "FACIAL", "offset": 69}, {"val": "FAILED", "offset": 85}, {"val": "1", "offset": 102}, {"val": "1", "offset": 122}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 60602000, "status": "passed"}, "line": 115, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 650000, "status": "passed"}, "line": 116, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 213000, "status": "passed"}, "line": 117, "name": "The error code should be 2003011 and message should be The factor provided is not as expected", "match": {"arguments": [{"val": "2003011", "offset": 25}, {"val": "The factor provided is not as expected", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:10.315Z", "before": [{"result": {"duration": 289000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 274000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 123, "name": "Fail to update step-up session - Factor mismatch", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-update-step-up-session---factor-mismatch;;4", "after": [{"result": {"duration": 129933000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 336000, "status": "passed"}, "line": 109, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 117999000, "status": "passed"}, "line": 110, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1995000, "status": "passed"}, "line": 111, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 85705000, "status": "passed"}, "line": 112, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 1222000, "status": "passed"}, "line": 113, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 779000, "status": "passed"}, "line": 114, "name": "I build the request body for updating a step-up session with factor: OTP, status: SUCCESS, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "OTP", "offset": 69}, {"val": "SUCCESS", "offset": 82}, {"val": "1", "offset": 100}, {"val": "1", "offset": 120}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 62794000, "status": "passed"}, "line": 115, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 1300000, "status": "passed"}, "line": 116, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 483000, "status": "passed"}, "line": 117, "name": "The error code should be 2003011 and message should be The factor provided is not as expected", "match": {"arguments": [{"val": "2003011", "offset": 25}, {"val": "The factor provided is not as expected", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:10.739Z", "before": [{"result": {"duration": 486000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 566000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 124, "name": "Fail to update step-up session - Factor mismatch", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-update-step-up-session---factor-mismatch;;5", "after": [{"result": {"duration": 116309000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 432000, "status": "passed"}, "line": 109, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 124187000, "status": "passed"}, "line": 110, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1881000, "status": "passed"}, "line": 111, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 94068000, "status": "passed"}, "line": 112, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 870000, "status": "passed"}, "line": 113, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 750000, "status": "passed"}, "line": 114, "name": "I build the request body for updating a step-up session with factor: PASSCODE, status: FAILED, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "PASSCODE", "offset": 69}, {"val": "FAILED", "offset": 87}, {"val": "1", "offset": 104}, {"val": "1", "offset": 124}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 60660000, "status": "passed"}, "line": 115, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 779000, "status": "passed"}, "line": 116, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 415000, "status": "passed"}, "line": 117, "name": "The error code should be 2003011 and message should be The factor provided is not as expected", "match": {"arguments": [{"val": "2003011", "offset": 25}, {"val": "The factor provided is not as expected", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:11.184Z", "before": [{"result": {"duration": 435000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 580000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 141, "name": "Fail to update step-up session - Session expired", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-update-step-up-session---session-expired;;2", "after": [{"result": {"duration": 56259000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 394000, "status": "passed"}, "line": 128, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 468000, "status": "passed"}, "line": 129, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2428000, "status": "passed"}, "line": 130, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 131447000, "status": "passed"}, "line": 131, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 1598000, "status": "passed"}, "line": 132, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 181534000, "status": "passed"}, "line": 133, "name": "I wait for the session to expire", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.iWaitForTheSessionToExpire()"}, "keyword": "And "}, {"result": {"duration": 1165000, "status": "passed"}, "line": 134, "name": "I build the request body for updating a step-up session with factor: OTP, status: SUCCESS, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "OTP", "offset": 69}, {"val": "SUCCESS", "offset": 82}, {"val": "1", "offset": 100}, {"val": "1", "offset": 120}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 156935000, "status": "passed"}, "line": 135, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 1006000, "status": "passed"}, "line": 136, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 406000, "status": "passed"}, "line": 137, "name": "The error code should be 2003002 and message should be Auth session expired", "match": {"arguments": [{"val": "2003002", "offset": 25}, {"val": "Auth session expired", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:11.741Z", "before": [{"result": {"duration": 434000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 348000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 142, "name": "Fail to update step-up session - Session expired", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-update-step-up-session---session-expired;;3", "after": [{"result": {"duration": 123353000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 455000, "status": "passed"}, "line": 128, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 133468000, "status": "passed"}, "line": 129, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2038000, "status": "passed"}, "line": 130, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 98792000, "status": "passed"}, "line": 131, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 664000, "status": "passed"}, "line": 132, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 153226000, "status": "passed"}, "line": 133, "name": "I wait for the session to expire", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.iWaitForTheSessionToExpire()"}, "keyword": "And "}, {"result": {"duration": 699000, "status": "passed"}, "line": 134, "name": "I build the request body for updating a step-up session with factor: DEVICE_BIO, status: FAILED, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 69}, {"val": "FAILED", "offset": 89}, {"val": "1", "offset": 106}, {"val": "1", "offset": 126}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 33066000, "status": "passed"}, "line": 135, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 1087000, "status": "passed"}, "line": 136, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 473000, "status": "passed"}, "line": 137, "name": "The error code should be 2003002 and message should be Auth session expired", "match": {"arguments": [{"val": "2003002", "offset": 25}, {"val": "Auth session expired", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:12.337Z", "before": [{"result": {"duration": 653000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 745000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 159, "name": "Fail to verify step-up session - Invalid request parameters", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-verify-step-up-session---invalid-request-parameters;;2", "after": [{"result": {"duration": 52268000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 664000, "status": "passed"}, "line": 147, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 608000, "status": "passed"}, "line": 148, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3439000, "status": "passed"}, "line": 149, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 97498000, "status": "passed"}, "line": 150, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 1236000, "status": "passed"}, "line": 151, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 3846000, "status": "passed"}, "line": 152, "name": "I build invalid verification request with missing stepUpAuthId", "match": {"arguments": [{"val": "stepUpAuthId", "offset": 50}], "location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.iBuildInvalidVerificationRequestWithMissing(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 12824000, "status": "passed"}, "line": 153, "name": "When update complete the customer go to verification step up with invalid request", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.whenUpdateCompleteTheCustomerGoToVerificationStepUpWithInvalidRequest()"}, "keyword": "And "}, {"result": {"duration": 1171000, "status": "passed"}, "line": 154, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 332000, "status": "passed"}, "line": 155, "name": "The error code should be 2003001 and message should be Invalid required fields: stepUpAuthId must not be blank", "match": {"arguments": [{"val": "2003001", "offset": 25}, {"val": "Invalid required fields: stepUpAuthId must not be blank", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailVerify"}]}, {"start_timestamp": "2025-07-17T03:21:12.534Z", "before": [{"result": {"duration": 290000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 300000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 160, "name": "Fail to verify step-up session - Invalid request parameters", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-verify-step-up-session---invalid-request-parameters;;3", "after": [{"result": {"duration": 51867000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 290000, "status": "passed"}, "line": 147, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 236000, "status": "passed"}, "line": 148, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1637000, "status": "passed"}, "line": 149, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 93172000, "status": "passed"}, "line": 150, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 663000, "status": "passed"}, "line": 151, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 601000, "status": "passed"}, "line": 152, "name": "I build invalid verification request with missing flowId", "match": {"arguments": [{"val": "flowId", "offset": 50}], "location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.iBuildInvalidVerificationRequestWithMissing(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 6174000, "status": "passed"}, "line": 153, "name": "When update complete the customer go to verification step up with invalid request", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.whenUpdateCompleteTheCustomerGoToVerificationStepUpWithInvalidRequest()"}, "keyword": "And "}, {"result": {"duration": 957000, "status": "passed"}, "line": 154, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 353000, "status": "passed"}, "line": 155, "name": "The error code should be 2003001 and message should be Invalid required fields: flowId must not be blank", "match": {"arguments": [{"val": "2003001", "offset": 25}, {"val": "Invalid required fields: flowId must not be blank", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailVerify"}]}, {"start_timestamp": "2025-07-17T03:21:12.710Z", "before": [{"result": {"duration": 504000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 371000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 161, "name": "Fail to verify step-up session - Invalid request parameters", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-verify-step-up-session---invalid-request-parameters;;4", "after": [{"result": {"duration": 112113000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 523000, "status": "passed"}, "line": 147, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 127100000, "status": "passed"}, "line": 148, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1934000, "status": "passed"}, "line": 149, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 95376000, "status": "passed"}, "line": 150, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 1187000, "status": "passed"}, "line": 151, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 842000, "status": "passed"}, "line": 152, "name": "I build invalid verification request with missing stepUpAuthId", "match": {"arguments": [{"val": "stepUpAuthId", "offset": 50}], "location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.iBuildInvalidVerificationRequestWithMissing(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 5699000, "status": "passed"}, "line": 153, "name": "When update complete the customer go to verification step up with invalid request", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.whenUpdateCompleteTheCustomerGoToVerificationStepUpWithInvalidRequest()"}, "keyword": "And "}, {"result": {"duration": 770000, "status": "passed"}, "line": 154, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 376000, "status": "passed"}, "line": 155, "name": "The error code should be 2003001 and message should be Invalid required fields: stepUpAuthId must not be blank", "match": {"arguments": [{"val": "2003001", "offset": 25}, {"val": "Invalid required fields: stepUpAuthId must not be blank", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailVerify"}]}, {"start_timestamp": "2025-07-17T03:21:13.090Z", "before": [{"result": {"duration": 444000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 794000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 177, "name": "Fail to verify step-up session - Session expired", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-verify-step-up-session---session-expired;;2", "after": [{"result": {"duration": 55540000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 673000, "status": "passed"}, "line": 165, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 572000, "status": "passed"}, "line": 166, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2634000, "status": "passed"}, "line": 167, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 80736000, "status": "passed"}, "line": 168, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 1087000, "status": "passed"}, "line": 169, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 138114000, "status": "passed"}, "line": 170, "name": "I wait for the session to expire", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.iWaitForTheSessionToExpire()"}, "keyword": "And "}, {"result": {"duration": 59267000, "status": "passed"}, "line": 171, "name": "When update complete the customer go to verification step up with flow id 123", "match": {"arguments": [{"val": " 123", "offset": 73}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.whenUpdateCompleteTheCustomerGoToVerificationStepUpWithFlowId(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 976000, "status": "passed"}, "line": 172, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 251000, "status": "passed"}, "line": 173, "name": "The error code should be 2003002 and message should be Auth session expired", "match": {"arguments": [{"val": "2003002", "offset": 25}, {"val": "Auth session expired", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailVerify"}]}, {"start_timestamp": "2025-07-17T03:21:13.455Z", "before": [{"result": {"duration": 448000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 519000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 178, "name": "Fail to verify step-up session - Session expired", "description": "", "id": "component-test-for-fail-cases-with-step-up-api;fail-to-verify-step-up-session---session-expired;;3", "after": [{"result": {"duration": 113548000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 624000, "status": "passed"}, "line": 165, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 121845000, "status": "passed"}, "line": 166, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1758000, "status": "passed"}, "line": 167, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 106785000, "status": "passed"}, "line": 168, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 648000, "status": "passed"}, "line": 169, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 152399000, "status": "passed"}, "line": 170, "name": "I wait for the session to expire", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.FailCaseStepDefinitions.iWaitForTheSessionToExpire()"}, "keyword": "And "}, {"result": {"duration": 21364000, "status": "passed"}, "line": 171, "name": "When update complete the customer go to verification step up with flow id 123", "match": {"arguments": [{"val": " 123", "offset": 73}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.whenUpdateCompleteTheCustomerGoToVerificationStepUpWithFlowId(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 854000, "status": "passed"}, "line": 172, "name": "The API should be return to status code: 400", "match": {"arguments": [{"val": "400", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 269000, "status": "passed"}, "line": 173, "name": "The error code should be 2003002 and message should be Auth session expired", "match": {"arguments": [{"val": "2003002", "offset": 25}, {"val": "Auth session expired", "offset": 55}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theErrorCodeShouldBeErrorCodeAndMessageShouldBeErrorMessage(java.lang.String,java.lang.String)"}, "keyword": "And "}], "tags": [{"name": "@StepUpFailCase"}, {"name": "@StepUpFailVerify"}]}], "name": "Component test for fail cases with Step-Up API", "description": "", "id": "component-test-for-fail-cases-with-step-up-api", "keyword": "Feature", "uri": "classpath:features/component/DemoFailCaseForStepUpAPI.feature", "tags": [{"name": "@StepUpFailCase", "type": "Tag", "location": {"line": 1, "column": 1}}]}, {"line": 3, "elements": [{"start_timestamp": "2025-07-17T03:21:14.020Z", "before": [{"result": {"duration": 408000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 309000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 14, "name": "Successfully init step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-init-step-up-authentication-session;;2", "after": [{"result": {"duration": 54811000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 383000, "status": "passed"}, "line": 7, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 264000, "status": "passed"}, "line": 8, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1800000, "status": "passed"}, "line": 9, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 58864000, "status": "passed"}, "line": 10, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 914000, "status": "passed"}, "line": 11, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpInit"}]}, {"start_timestamp": "2025-07-17T03:21:14.163Z", "before": [{"result": {"duration": 240000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 248000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 15, "name": "Successfully init step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-init-step-up-authentication-session;;3", "after": [{"result": {"duration": 52136000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 267000, "status": "passed"}, "line": 7, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 241000, "status": "passed"}, "line": 8, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1363000, "status": "passed"}, "line": 9, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 92759000, "status": "passed"}, "line": 10, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 919000, "status": "passed"}, "line": 11, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpInit"}]}, {"start_timestamp": "2025-07-17T03:21:14.327Z", "before": [{"result": {"duration": 312000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 311000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 16, "name": "Successfully init step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-init-step-up-authentication-session;;4", "after": [{"result": {"duration": 110329000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 301000, "status": "passed"}, "line": 7, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 111880000, "status": "passed"}, "line": 8, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1284000, "status": "passed"}, "line": 9, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 87275000, "status": "passed"}, "line": 10, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 606000, "status": "passed"}, "line": 11, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpInit"}]}, {"start_timestamp": "2025-07-17T03:21:14.655Z", "before": [{"result": {"duration": 372000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 493000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 17, "name": "Successfully init step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-init-step-up-authentication-session;;5", "after": [{"result": {"duration": 109325000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 376000, "status": "passed"}, "line": 7, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 117631000, "status": "passed"}, "line": 8, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1845000, "status": "passed"}, "line": 9, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 84698000, "status": "passed"}, "line": 10, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 561000, "status": "passed"}, "line": 11, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpInit"}]}, {"start_timestamp": "2025-07-17T03:21:14.991Z", "before": [{"result": {"duration": 271000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 254000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 18, "name": "Successfully init step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-init-step-up-authentication-session;;6", "after": [{"result": {"duration": 49716000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 288000, "status": "passed"}, "line": 7, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 344000, "status": "passed"}, "line": 8, "name": "The profile contains valid \"facial\" information", "match": {"arguments": [{"val": "\"facial\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2053000, "status": "passed"}, "line": 9, "name": "I build the request body for initializing a step-up session with factor FACIAL, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/FACIAL_authConfig.json", "match": {"arguments": [{"val": "FACIAL", "offset": 72}, {"val": " 123", "offset": 88}, {"val": "PROFILE_ID", "offset": 108}, {"val": "/dataShare/successCase/FACIAL_authConfig.json", "offset": 136}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 99938000, "status": "passed"}, "line": 10, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 494000, "status": "passed"}, "line": 11, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpInit"}]}, {"start_timestamp": "2025-07-17T03:21:15.158Z", "before": [{"result": {"duration": 264000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 255000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 19, "name": "Successfully init step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-init-step-up-authentication-session;;7", "after": [{"result": {"duration": 50492000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 359000, "status": "passed"}, "line": 7, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 220000, "status": "passed"}, "line": 8, "name": "The profile contains valid \"facial\" information", "match": {"arguments": [{"val": "\"facial\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1106000, "status": "passed"}, "line": 9, "name": "I build the request body for initializing a step-up session with factor FACIAL, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/FACIAL_authConfig.json", "match": {"arguments": [{"val": "FACIAL", "offset": 72}, {"val": " 123", "offset": 88}, {"val": "PROFILE_ID", "offset": 108}, {"val": "/dataShare/successCase/FACIAL_authConfig.json", "offset": 136}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 81356000, "status": "passed"}, "line": 10, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 811000, "status": "passed"}, "line": 11, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpInit"}]}, {"start_timestamp": "2025-07-17T03:21:15.307Z", "before": [{"result": {"duration": 204000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 227000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 20, "name": "Successfully init step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-init-step-up-authentication-session;;8", "after": [{"result": {"duration": 108847000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 207000, "status": "passed"}, "line": 7, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 111749000, "status": "passed"}, "line": 8, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1539000, "status": "passed"}, "line": 9, "name": "I build the request body for initializing a step-up session with factor PASSCODE, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/PASS_CODE_authConfig.json", "match": {"arguments": [{"val": "PASSCODE", "offset": 72}, {"val": " 123", "offset": 90}, {"val": "PROFILE_ID", "offset": 110}, {"val": "/dataShare/successCase/PASS_CODE_authConfig.json", "offset": 138}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 81410000, "status": "passed"}, "line": 10, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 593000, "status": "passed"}, "line": 11, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpInit"}]}, {"start_timestamp": "2025-07-17T03:21:15.626Z", "before": [{"result": {"duration": 246000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 258000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 32, "name": "Successfully validation step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-validation-step-up-authentication-session;;2", "after": [{"result": {"duration": 51996000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 225000, "status": "passed"}, "line": 24, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 198000, "status": "passed"}, "line": 25, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1353000, "status": "passed"}, "line": 26, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 88284000, "status": "passed"}, "line": 27, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 58671000, "status": "passed"}, "line": 28, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: true", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "true", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 658000, "status": "passed"}, "line": 29, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpValidation"}]}, {"start_timestamp": "2025-07-17T03:21:15.844Z", "before": [{"result": {"duration": 288000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 272000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 33, "name": "Successfully validation step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-validation-step-up-authentication-session;;3", "after": [{"result": {"duration": 49991000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 238000, "status": "passed"}, "line": 24, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 219000, "status": "passed"}, "line": 25, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1106000, "status": "passed"}, "line": 26, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 83427000, "status": "passed"}, "line": 27, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 54218000, "status": "passed"}, "line": 28, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: false", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "false", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 583000, "status": "passed"}, "line": 29, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpValidation"}]}, {"start_timestamp": "2025-07-17T03:21:16.049Z", "before": [{"result": {"duration": 289000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 281000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 34, "name": "Successfully validation step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-validation-step-up-authentication-session;;4", "after": [{"result": {"duration": 106538000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 242000, "status": "passed"}, "line": 24, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 117916000, "status": "passed"}, "line": 25, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1440000, "status": "passed"}, "line": 26, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 83127000, "status": "passed"}, "line": 27, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 55831000, "status": "passed"}, "line": 28, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: true", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "true", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 694000, "status": "passed"}, "line": 29, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpValidation"}]}, {"start_timestamp": "2025-07-17T03:21:16.429Z", "before": [{"result": {"duration": 220000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 242000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 35, "name": "Successfully validation step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-validation-step-up-authentication-session;;5", "after": [{"result": {"duration": 109489000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 217000, "status": "passed"}, "line": 24, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 112141000, "status": "passed"}, "line": 25, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1552000, "status": "passed"}, "line": 26, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 81199000, "status": "passed"}, "line": 27, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 55541000, "status": "passed"}, "line": 28, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: false", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "false", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 969000, "status": "passed"}, "line": 29, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpValidation"}]}, {"start_timestamp": "2025-07-17T03:21:16.804Z", "before": [{"result": {"duration": 220000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 235000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 36, "name": "Successfully validation step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-validation-step-up-authentication-session;;6", "after": [{"result": {"duration": 51903000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 200000, "status": "passed"}, "line": 24, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 157000, "status": "passed"}, "line": 25, "name": "The profile contains valid \"facial\" information", "match": {"arguments": [{"val": "\"facial\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1225000, "status": "passed"}, "line": 26, "name": "I build the request body for initializing a step-up session with factor FACIAL, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/FACIAL_authConfig.json", "match": {"arguments": [{"val": "FACIAL", "offset": 72}, {"val": " 123", "offset": 88}, {"val": "PROFILE_ID", "offset": 108}, {"val": "/dataShare/successCase/FACIAL_authConfig.json", "offset": 136}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 81687000, "status": "passed"}, "line": 27, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 56080000, "status": "passed"}, "line": 28, "name": "When the customer go to validation step up with factor: FACIAL and includeFactorConfig: true", "match": {"arguments": [{"val": "FACIAL", "offset": 56}, {"val": "true", "offset": 88}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 725000, "status": "passed"}, "line": 29, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpValidation"}]}, {"start_timestamp": "2025-07-17T03:21:17.011Z", "before": [{"result": {"duration": 238000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 281000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 37, "name": "Successfully validation step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-validation-step-up-authentication-session;;7", "after": [{"result": {"duration": 50339000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 233000, "status": "passed"}, "line": 24, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 214000, "status": "passed"}, "line": 25, "name": "The profile contains valid \"facial\" information", "match": {"arguments": [{"val": "\"facial\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1178000, "status": "passed"}, "line": 26, "name": "I build the request body for initializing a step-up session with factor FACIAL, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/FACIAL_authConfig.json", "match": {"arguments": [{"val": "FACIAL", "offset": 72}, {"val": " 123", "offset": 88}, {"val": "PROFILE_ID", "offset": 108}, {"val": "/dataShare/successCase/FACIAL_authConfig.json", "offset": 136}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 82914000, "status": "passed"}, "line": 27, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 55840000, "status": "passed"}, "line": 28, "name": "When the customer go to validation step up with factor: FACIAL and includeFactorConfig: false", "match": {"arguments": [{"val": "FACIAL", "offset": 56}, {"val": "false", "offset": 88}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 511000, "status": "passed"}, "line": 29, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpValidation"}]}, {"start_timestamp": "2025-07-17T03:21:17.217Z", "before": [{"result": {"duration": 234000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 276000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 38, "name": "Successfully validation step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-validation-step-up-authentication-session;;8", "after": [{"result": {"duration": 51743000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 240000, "status": "passed"}, "line": 24, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 183000, "status": "passed"}, "line": 25, "name": "The profile contains valid \"passcode\" information", "match": {"arguments": [{"val": "\"passcode\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1048000, "status": "passed"}, "line": 26, "name": "I build the request body for initializing a step-up session with factor PASSCODE, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/PASS_CODE_authConfig.json", "match": {"arguments": [{"val": "PASSCODE", "offset": 72}, {"val": " 123", "offset": 90}, {"val": "PROFILE_ID", "offset": 110}, {"val": "/dataShare/successCase/PASS_CODE_authConfig.json", "offset": 138}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 83190000, "status": "passed"}, "line": 27, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 55945000, "status": "passed"}, "line": 28, "name": "When the customer go to validation step up with factor: PASSCODE and includeFactorConfig: true", "match": {"arguments": [{"val": "PASSCODE", "offset": 56}, {"val": "true", "offset": 90}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 651000, "status": "passed"}, "line": 29, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpValidation"}]}, {"start_timestamp": "2025-07-17T03:21:17.427Z", "before": [{"result": {"duration": 392000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 495000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 39, "name": "Successfully validation step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-validation-step-up-authentication-session;;9", "after": [{"result": {"duration": 51484000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 283000, "status": "passed"}, "line": 24, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 205000, "status": "passed"}, "line": 25, "name": "The profile contains valid \"passcode\" information", "match": {"arguments": [{"val": "\"passcode\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1200000, "status": "passed"}, "line": 26, "name": "I build the request body for initializing a step-up session with factor PASSCODE, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/PASS_CODE_authConfig.json", "match": {"arguments": [{"val": "PASSCODE", "offset": 72}, {"val": " 123", "offset": 90}, {"val": "PROFILE_ID", "offset": 110}, {"val": "/dataShare/successCase/PASS_CODE_authConfig.json", "offset": 138}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 81145000, "status": "passed"}, "line": 27, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 53881000, "status": "passed"}, "line": 28, "name": "When the customer go to validation step up with factor: PASSCODE and includeFactorConfig: false", "match": {"arguments": [{"val": "PASSCODE", "offset": 56}, {"val": "false", "offset": 90}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 631000, "status": "passed"}, "line": 29, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpValidation"}]}, {"start_timestamp": "2025-07-17T03:21:17.630Z", "before": [{"result": {"duration": 299000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 356000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 52, "name": "Successfully update step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-update-step-up-authentication-session;;2", "after": [{"result": {"duration": 52840000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 331000, "status": "passed"}, "line": 43, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 268000, "status": "passed"}, "line": 44, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1394000, "status": "passed"}, "line": 45, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 79610000, "status": "passed"}, "line": 46, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 365000, "status": "passed"}, "line": 47, "name": "I build the request body for updating a step-up session with factor: OTP, status: SUCCESS, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "OTP", "offset": 69}, {"val": "SUCCESS", "offset": 82}, {"val": "1", "offset": 100}, {"val": "1", "offset": 120}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 130909000, "status": "passed"}, "line": 48, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 289000, "status": "passed"}, "line": 49, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:17.921Z", "before": [{"result": {"duration": 276000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 394000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 53, "name": "Successfully update step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-update-step-up-authentication-session;;3", "after": [{"result": {"duration": 50348000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 425000, "status": "passed"}, "line": 43, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 254000, "status": "passed"}, "line": 44, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1883000, "status": "passed"}, "line": 45, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 100691000, "status": "passed"}, "line": 46, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 546000, "status": "passed"}, "line": 47, "name": "I build the request body for updating a step-up session with factor: OTP, status: FAILED, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "OTP", "offset": 69}, {"val": "FAILED", "offset": 82}, {"val": "1", "offset": 99}, {"val": "1", "offset": 119}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 133001000, "status": "passed"}, "line": 48, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 262000, "status": "passed"}, "line": 49, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:18.225Z", "before": [{"result": {"duration": 411000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 244000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 54, "name": "Successfully update step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-update-step-up-authentication-session;;4", "after": [{"result": {"duration": 107433000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 255000, "status": "passed"}, "line": 43, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 115512000, "status": "passed"}, "line": 44, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1373000, "status": "passed"}, "line": 45, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 86970000, "status": "passed"}, "line": 46, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 397000, "status": "passed"}, "line": 47, "name": "I build the request body for updating a step-up session with factor: DEVICE_BIO, status: SUCCESS, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 69}, {"val": "SUCCESS", "offset": 89}, {"val": "1", "offset": 107}, {"val": "1", "offset": 127}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 165361000, "status": "passed"}, "line": 48, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 213000, "status": "passed"}, "line": 49, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:18.719Z", "before": [{"result": {"duration": 350000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 319000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 55, "name": "Successfully update step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-update-step-up-authentication-session;;5", "after": [{"result": {"duration": 107674000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 267000, "status": "passed"}, "line": 43, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 117502000, "status": "passed"}, "line": 44, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2524000, "status": "passed"}, "line": 45, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 81076000, "status": "passed"}, "line": 46, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 617000, "status": "passed"}, "line": 47, "name": "I build the request body for updating a step-up session with factor: DEVICE_BIO, status: FAILED, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 69}, {"val": "FAILED", "offset": 89}, {"val": "1", "offset": 106}, {"val": "1", "offset": 126}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 136921000, "status": "passed"}, "line": 48, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 208000, "status": "passed"}, "line": 49, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:19.181Z", "before": [{"result": {"duration": 268000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 464000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 56, "name": "Successfully update step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-update-step-up-authentication-session;;6", "after": [{"result": {"duration": 51854000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 254000, "status": "passed"}, "line": 43, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 233000, "status": "passed"}, "line": 44, "name": "The profile contains valid \"facial\" information", "match": {"arguments": [{"val": "\"facial\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1521000, "status": "passed"}, "line": 45, "name": "I build the request body for initializing a step-up session with factor FACIAL, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/FACIAL_authConfig.json", "match": {"arguments": [{"val": "FACIAL", "offset": 72}, {"val": " 123", "offset": 88}, {"val": "PROFILE_ID", "offset": 108}, {"val": "/dataShare/successCase/FACIAL_authConfig.json", "offset": 136}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 85828000, "status": "passed"}, "line": 46, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 645000, "status": "passed"}, "line": 47, "name": "I build the request body for updating a step-up session with factor: FACIAL, status: SUCCESS, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "FACIAL", "offset": 69}, {"val": "SUCCESS", "offset": 85}, {"val": "1", "offset": 103}, {"val": "1", "offset": 123}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 135001000, "status": "passed"}, "line": 48, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 412000, "status": "passed"}, "line": 49, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:19.483Z", "before": [{"result": {"duration": 342000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 382000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 57, "name": "Successfully update step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-update-step-up-authentication-session;;7", "after": [{"result": {"duration": 52466000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 283000, "status": "passed"}, "line": 43, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 275000, "status": "passed"}, "line": 44, "name": "The profile contains valid \"facial\" information", "match": {"arguments": [{"val": "\"facial\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1246000, "status": "passed"}, "line": 45, "name": "I build the request body for initializing a step-up session with factor FACIAL, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/FACIAL_authConfig.json", "match": {"arguments": [{"val": "FACIAL", "offset": 72}, {"val": " 123", "offset": 88}, {"val": "PROFILE_ID", "offset": 108}, {"val": "/dataShare/successCase/FACIAL_authConfig.json", "offset": 136}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 95658000, "status": "passed"}, "line": 46, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 427000, "status": "passed"}, "line": 47, "name": "I build the request body for updating a step-up session with factor: FACIAL, status: FAILED, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "FACIAL", "offset": 69}, {"val": "FAILED", "offset": 85}, {"val": "1", "offset": 102}, {"val": "1", "offset": 122}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 133410000, "status": "passed"}, "line": 48, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 287000, "status": "passed"}, "line": 49, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:19.783Z", "before": [{"result": {"duration": 361000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 403000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 58, "name": "Successfully update step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-update-step-up-authentication-session;;8", "after": [{"result": {"duration": 52461000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 282000, "status": "passed"}, "line": 43, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 245000, "status": "passed"}, "line": 44, "name": "The profile contains valid \"passcode\" information", "match": {"arguments": [{"val": "\"passcode\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1517000, "status": "passed"}, "line": 45, "name": "I build the request body for initializing a step-up session with factor PASSCODE, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/PASS_CODE_authConfig.json", "match": {"arguments": [{"val": "PASSCODE", "offset": 72}, {"val": " 123", "offset": 90}, {"val": "PROFILE_ID", "offset": 110}, {"val": "/dataShare/successCase/PASS_CODE_authConfig.json", "offset": 138}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 98729000, "status": "passed"}, "line": 46, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 503000, "status": "passed"}, "line": 47, "name": "I build the request body for updating a step-up session with factor: PASSCODE, status: SUCCESS, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "PASSCODE", "offset": 69}, {"val": "SUCCESS", "offset": 87}, {"val": "1", "offset": 105}, {"val": "1", "offset": 125}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 130175000, "status": "passed"}, "line": 48, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 270000, "status": "passed"}, "line": 49, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:20.084Z", "before": [{"result": {"duration": 349000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 392000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 59, "name": "Successfully update step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-update-step-up-authentication-session;;9", "after": [{"result": {"duration": 51896000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 330000, "status": "passed"}, "line": 43, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 323000, "status": "passed"}, "line": 44, "name": "The profile contains valid \"passcode\" information", "match": {"arguments": [{"val": "\"passcode\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1836000, "status": "passed"}, "line": 45, "name": "I build the request body for initializing a step-up session with factor PASSCODE, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/PASS_CODE_authConfig.json", "match": {"arguments": [{"val": "PASSCODE", "offset": 72}, {"val": " 123", "offset": 90}, {"val": "PROFILE_ID", "offset": 110}, {"val": "/dataShare/successCase/PASS_CODE_authConfig.json", "offset": 138}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 93505000, "status": "passed"}, "line": 46, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 420000, "status": "passed"}, "line": 47, "name": "I build the request body for updating a step-up session with factor: PASSCODE, status: FAILED, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "PASSCODE", "offset": 69}, {"val": "FAILED", "offset": 87}, {"val": "1", "offset": 104}, {"val": "1", "offset": 124}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 135815000, "status": "passed"}, "line": 48, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 437000, "status": "passed"}, "line": 49, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpUpdate"}]}, {"start_timestamp": "2025-07-17T03:21:20.384Z", "before": [{"result": {"duration": 244000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 333000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 74, "name": "Successfully verification step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-verification-step-up-authentication-session;;2", "after": [{"result": {"duration": 50799000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 278000, "status": "passed"}, "line": 63, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 170000, "status": "passed"}, "line": 64, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1155000, "status": "passed"}, "line": 65, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 90266000, "status": "passed"}, "line": 66, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 57170000, "status": "passed"}, "line": 67, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: <includeFactorConfig>", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "<includeFactorConfig>", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 558000, "status": "passed"}, "line": 68, "name": "I build the request body for updating a step-up session with factor: OTP, status: SUCCESS, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "OTP", "offset": 69}, {"val": "SUCCESS", "offset": 82}, {"val": "1", "offset": 100}, {"val": "1", "offset": 120}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 133727000, "status": "passed"}, "line": 69, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 137666000, "status": "passed"}, "line": 70, "name": "When update complete the customer go to verification step up with flow id 123", "match": {"arguments": [{"val": " 123", "offset": 73}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.whenUpdateCompleteTheCustomerGoToVerificationStepUpWithFlowId(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 636000, "status": "passed"}, "line": 71, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpVerification"}]}, {"start_timestamp": "2025-07-17T03:21:20.872Z", "before": [{"result": {"duration": 263000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 241000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 75, "name": "Successfully verification step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-verification-step-up-authentication-session;;3", "after": [{"result": {"duration": 51317000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 239000, "status": "passed"}, "line": 63, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 175000, "status": "passed"}, "line": 64, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1070000, "status": "passed"}, "line": 65, "name": "I build the request body for initializing a step-up session with factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 72}, {"val": " 123", "offset": 85}, {"val": "PROFILE_ID", "offset": 105}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 133}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 93238000, "status": "passed"}, "line": 66, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 55905000, "status": "passed"}, "line": 67, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: <includeFactorConfig>", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "<includeFactorConfig>", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 429000, "status": "passed"}, "line": 68, "name": "I build the request body for updating a step-up session with factor: OTP, status: FAILED, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "OTP", "offset": 69}, {"val": "FAILED", "offset": 82}, {"val": "1", "offset": 99}, {"val": "1", "offset": 119}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 155607000, "status": "passed"}, "line": 69, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 73031000, "status": "passed"}, "line": 70, "name": "When update complete the customer go to verification step up with flow id 123", "match": {"arguments": [{"val": " 123", "offset": 73}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.whenUpdateCompleteTheCustomerGoToVerificationStepUpWithFlowId(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 514000, "status": "passed"}, "line": 71, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpVerification"}]}, {"start_timestamp": "2025-07-17T03:21:21.319Z", "before": [{"result": {"duration": 232000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 416000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 76, "name": "Successfully verification step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-verification-step-up-authentication-session;;4", "after": [{"result": {"duration": 105459000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 203000, "status": "passed"}, "line": 63, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 113010000, "status": "passed"}, "line": 64, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1166000, "status": "passed"}, "line": 65, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 77498000, "status": "passed"}, "line": 66, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 54154000, "status": "passed"}, "line": 67, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: <includeFactorConfig>", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "<includeFactorConfig>", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 453000, "status": "passed"}, "line": 68, "name": "I build the request body for updating a step-up session with factor: DEVICE_BIO, status: SUCCESS, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 69}, {"val": "SUCCESS", "offset": 89}, {"val": "1", "offset": 107}, {"val": "1", "offset": 127}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 141706000, "status": "passed"}, "line": 69, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 90037000, "status": "passed"}, "line": 70, "name": "When update complete the customer go to verification step up with flow id 123", "match": {"arguments": [{"val": " 123", "offset": 73}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.whenUpdateCompleteTheCustomerGoToVerificationStepUpWithFlowId(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 496000, "status": "passed"}, "line": 71, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpVerification"}]}, {"start_timestamp": "2025-07-17T03:21:21.917Z", "before": [{"result": {"duration": 236000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 220000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 77, "name": "Successfully verification step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-verification-step-up-authentication-session;;5", "after": [{"result": {"duration": 108974000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 215000, "status": "passed"}, "line": 63, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 113257000, "status": "passed"}, "line": 64, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1476000, "status": "passed"}, "line": 65, "name": "I build the request body for initializing a step-up session with factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 72}, {"val": " 123", "offset": 92}, {"val": "PROFILE_ID", "offset": 112}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 140}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 82837000, "status": "passed"}, "line": 66, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 56797000, "status": "passed"}, "line": 67, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: <includeFactorConfig>", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "<includeFactorConfig>", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 564000, "status": "passed"}, "line": 68, "name": "I build the request body for updating a step-up session with factor: DEVICE_BIO, status: FAILED, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 69}, {"val": "FAILED", "offset": 89}, {"val": "1", "offset": 106}, {"val": "1", "offset": 126}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 138973000, "status": "passed"}, "line": 69, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 17705000, "status": "passed"}, "line": 70, "name": "When update complete the customer go to verification step up with flow id 123", "match": {"arguments": [{"val": " 123", "offset": 73}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.whenUpdateCompleteTheCustomerGoToVerificationStepUpWithFlowId(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 448000, "status": "passed"}, "line": 71, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpVerification"}]}, {"start_timestamp": "2025-07-17T03:21:22.458Z", "before": [{"result": {"duration": 869000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 327000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 78, "name": "Successfully verification step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-verification-step-up-authentication-session;;6", "after": [{"result": {"duration": 52431000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 688000, "status": "passed"}, "line": 63, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 273000, "status": "passed"}, "line": 64, "name": "The profile contains valid \"facial\" information", "match": {"arguments": [{"val": "\"facial\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2096000, "status": "passed"}, "line": 65, "name": "I build the request body for initializing a step-up session with factor FACIAL, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/FACIAL_authConfig.json", "match": {"arguments": [{"val": "FACIAL", "offset": 72}, {"val": " 123", "offset": 88}, {"val": "PROFILE_ID", "offset": 108}, {"val": "/dataShare/successCase/FACIAL_authConfig.json", "offset": 136}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 104813000, "status": "passed"}, "line": 66, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 58584000, "status": "passed"}, "line": 67, "name": "When the customer go to validation step up with factor: FACIAL and includeFactorConfig: <includeFactorConfig>", "match": {"arguments": [{"val": "FACIAL", "offset": 56}, {"val": "<includeFactorConfig>", "offset": 88}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 382000, "status": "passed"}, "line": 68, "name": "I build the request body for updating a step-up session with factor: FACIAL, status: SUCCESS, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "FACIAL", "offset": 69}, {"val": "SUCCESS", "offset": 85}, {"val": "1", "offset": 103}, {"val": "1", "offset": 123}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 134003000, "status": "passed"}, "line": 69, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 96902000, "status": "passed"}, "line": 70, "name": "When update complete the customer go to verification step up with flow id 123", "match": {"arguments": [{"val": " 123", "offset": 73}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.whenUpdateCompleteTheCustomerGoToVerificationStepUpWithFlowId(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 551000, "status": "passed"}, "line": 71, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpVerification"}]}, {"start_timestamp": "2025-07-17T03:21:22.927Z", "before": [{"result": {"duration": 204000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 342000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 79, "name": "Successfully verification step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-verification-step-up-authentication-session;;7", "after": [{"result": {"duration": 53984000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 231000, "status": "passed"}, "line": 63, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 165000, "status": "passed"}, "line": 64, "name": "The profile contains valid \"facial\" information", "match": {"arguments": [{"val": "\"facial\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1178000, "status": "passed"}, "line": 65, "name": "I build the request body for initializing a step-up session with factor FACIAL, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/FACIAL_authConfig.json", "match": {"arguments": [{"val": "FACIAL", "offset": 72}, {"val": " 123", "offset": 88}, {"val": "PROFILE_ID", "offset": 108}, {"val": "/dataShare/successCase/FACIAL_authConfig.json", "offset": 136}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 81364000, "status": "passed"}, "line": 66, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 56190000, "status": "passed"}, "line": 67, "name": "When the customer go to validation step up with factor: FACIAL and includeFactorConfig: <includeFactorConfig>", "match": {"arguments": [{"val": "FACIAL", "offset": 56}, {"val": "<includeFactorConfig>", "offset": 88}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 421000, "status": "passed"}, "line": 68, "name": "I build the request body for updating a step-up session with factor: FACIAL, status: FAILED, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "FACIAL", "offset": 69}, {"val": "FAILED", "offset": 85}, {"val": "1", "offset": 102}, {"val": "1", "offset": 122}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 137298000, "status": "passed"}, "line": 69, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 20374000, "status": "passed"}, "line": 70, "name": "When update complete the customer go to verification step up with flow id 123", "match": {"arguments": [{"val": " 123", "offset": 73}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.whenUpdateCompleteTheCustomerGoToVerificationStepUpWithFlowId(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 561000, "status": "passed"}, "line": 71, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpVerification"}]}, {"start_timestamp": "2025-07-17T03:21:23.299Z", "before": [{"result": {"duration": 342000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 480000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 80, "name": "Successfully verification step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-verification-step-up-authentication-session;;8", "after": [{"result": {"duration": 52087000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 306000, "status": "passed"}, "line": 63, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 281000, "status": "passed"}, "line": 64, "name": "The profile contains valid \"facial\" information", "match": {"arguments": [{"val": "\"facial\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1687000, "status": "passed"}, "line": 65, "name": "I build the request body for initializing a step-up session with factor FACIAL, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/FACIAL_authConfig.json", "match": {"arguments": [{"val": "FACIAL", "offset": 72}, {"val": " 123", "offset": 88}, {"val": "PROFILE_ID", "offset": 108}, {"val": "/dataShare/successCase/FACIAL_authConfig.json", "offset": 136}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 96405000, "status": "passed"}, "line": 66, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 54298000, "status": "passed"}, "line": 67, "name": "When the customer go to validation step up with factor: FACIAL and includeFactorConfig: <includeFactorConfig>", "match": {"arguments": [{"val": "FACIAL", "offset": 56}, {"val": "<includeFactorConfig>", "offset": 88}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 452000, "status": "passed"}, "line": 68, "name": "I build the request body for updating a step-up session with factor: FACIAL, status: FAILED, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "FACIAL", "offset": 69}, {"val": "FAILED", "offset": 85}, {"val": "1", "offset": 102}, {"val": "1", "offset": 122}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 133969000, "status": "passed"}, "line": 69, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 16930000, "status": "passed"}, "line": 70, "name": "When update complete the customer go to verification step up with flow id 123", "match": {"arguments": [{"val": " 123", "offset": 73}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.whenUpdateCompleteTheCustomerGoToVerificationStepUpWithFlowId(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 589000, "status": "passed"}, "line": 71, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpVerification"}]}, {"start_timestamp": "2025-07-17T03:21:23.674Z", "before": [{"result": {"duration": 359000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 327000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 81, "name": "Successfully verification step-up authentication session", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-verification-step-up-authentication-session;;9", "after": [{"result": {"duration": 109288000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 254000, "status": "passed"}, "line": 63, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 114341000, "status": "passed"}, "line": 64, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 1264000, "status": "passed"}, "line": 65, "name": "I build the request body for initializing a step-up session with factor PASSCODE, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/PASS_CODE_authConfig.json", "match": {"arguments": [{"val": "PASSCODE", "offset": 72}, {"val": " 123", "offset": 90}, {"val": "PROFILE_ID", "offset": 110}, {"val": "/dataShare/successCase/PASS_CODE_authConfig.json", "offset": 138}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 85532000, "status": "passed"}, "line": 66, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 57827000, "status": "passed"}, "line": 67, "name": "When the customer go to validation step up with factor: PASSCODE and includeFactorConfig: <includeFactorConfig>", "match": {"arguments": [{"val": "PASSCODE", "offset": 56}, {"val": "<includeFactorConfig>", "offset": 90}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 356000, "status": "passed"}, "line": 68, "name": "I build the request body for updating a step-up session with factor: PASSCODE, status: SUCCESS, attempt: 1, and maxAttempts: 1", "match": {"arguments": [{"val": "PASSCODE", "offset": 69}, {"val": "SUCCESS", "offset": 87}, {"val": "1", "offset": 105}, {"val": "1", "offset": 125}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iBuildTheRequestBodyForUpdatingAStepUpSessionWithFactorFactorStatusStatusAttemptAttemptAndMaxAttemptsMaxAttempts(java.lang.String,java.lang.String,int,int)"}, "keyword": "And "}, {"result": {"duration": 135446000, "status": "passed"}, "line": 69, "name": "Next customer go to update step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.DemoUpdateStepUp.iGoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 135092000, "status": "passed"}, "line": 70, "name": "When update complete the customer go to verification step up with flow id 123", "match": {"arguments": [{"val": " 123", "offset": 73}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.whenUpdateCompleteTheCustomerGoToVerificationStepUpWithFlowId(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 533000, "status": "passed"}, "line": 71, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpVerification"}]}, {"start_timestamp": "2025-07-17T03:21:24.329Z", "before": [{"result": {"duration": 198000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 209000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 97, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;2", "after": [{"result": {"duration": 53640000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 195000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 149000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2705000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 87}, {"val": " 123", "offset": 100}, {"val": "PROFILE_ID", "offset": 120}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 148}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 82159000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 871000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 56529000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: true", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "true", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 731000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 116325000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"INELIGIBLE\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"INELIGIBLE\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 53}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 1039000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:24.662Z", "before": [{"result": {"duration": 440000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 493000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 98, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;3", "after": [{"result": {"duration": 51211000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 487000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 355000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3991000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 87}, {"val": " 123", "offset": 100}, {"val": "PROFILE_ID", "offset": 120}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 148}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 99949000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 680000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 55940000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: false", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "false", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 475000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 104343000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"INELIGIBLE\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"INELIGIBLE\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 53}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 716000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:24.995Z", "before": [{"result": {"duration": 220000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 197000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 99, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;4", "after": [{"result": {"duration": 50313000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 195000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 152000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2871000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 87}, {"val": " 123", "offset": 100}, {"val": "PROFILE_ID", "offset": 120}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 148}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 82370000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 421000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 55275000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: true", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "true", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 638000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 102171000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"CANCELLED\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"CANCELLED\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 52}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 580000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:25.305Z", "before": [{"result": {"duration": 293000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 343000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 100, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;5", "after": [{"result": {"duration": 51896000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 256000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 235000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2775000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 87}, {"val": " 123", "offset": 100}, {"val": "PROFILE_ID", "offset": 120}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 148}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 90009000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 551000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 55651000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: false", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "false", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 669000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 102321000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"CANCELLED\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"CANCELLED\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 52}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 672000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:25.625Z", "before": [{"result": {"duration": 241000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 222000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 101, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;6", "after": [{"result": {"duration": 52873000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 215000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 221000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2673000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 87}, {"val": " 123", "offset": 100}, {"val": "PROFILE_ID", "offset": 120}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 148}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 96668000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 490000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 56658000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: true", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "true", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 580000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 111703000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"FAILED_CHANNEL\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"FAILED_CHANNEL\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 57}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 615000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:25.962Z", "before": [{"result": {"duration": 272000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 221000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 102, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;7", "after": [{"result": {"duration": 55314000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 207000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 161000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2211000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 87}, {"val": " 123", "offset": 100}, {"val": "PROFILE_ID", "offset": 120}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 148}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 88267000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 535000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 55355000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: false", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "false", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 724000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 104672000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"FAILED_CHANNEL\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"FAILED_CHANNEL\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 57}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 624000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:26.294Z", "before": [{"result": {"duration": 384000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 869000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 103, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;8", "after": [{"result": {"duration": 50873000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 398000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 373000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 4932000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 87}, {"val": " 123", "offset": 100}, {"val": "PROFILE_ID", "offset": 120}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 148}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 69534000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 881000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 156139000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: true", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "true", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 795000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 160438000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"INELIGIB<PERSON>\" for \"UNAUTHORIZED\" controller", "match": {"arguments": [{"val": "\"INELIGIBLE\"", "offset": 36}, {"val": "\"UNAUTHORIZED\"", "offset": 53}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 535000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:26.756Z", "before": [{"result": {"duration": 370000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 249000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 104, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;9", "after": [{"result": {"duration": 53530000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 217000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 369000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3312000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 87}, {"val": " 123", "offset": 100}, {"val": "PROFILE_ID", "offset": 120}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 148}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 96122000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 666000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 55990000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: false", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "false", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 518000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 104514000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"INELIGIB<PERSON>\" for \"UNAUTHORIZED\" controller", "match": {"arguments": [{"val": "\"INELIGIBLE\"", "offset": 36}, {"val": "\"UNAUTHORIZED\"", "offset": 53}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 766000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:27.089Z", "before": [{"result": {"duration": 243000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 235000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 105, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;10", "after": [{"result": {"duration": 50919000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 242000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 368000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3453000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 87}, {"val": " 123", "offset": 100}, {"val": "PROFILE_ID", "offset": 120}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 148}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 93565000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 508000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 56306000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: true", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "true", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 512000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 106014000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"CANCELLED\" for \"UNAUTHORIZED\" controller", "match": {"arguments": [{"val": "\"CANCELLED\"", "offset": 36}, {"val": "\"UNAUTHORIZED\"", "offset": 52}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 449000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:27.418Z", "before": [{"result": {"duration": 233000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 353000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 106, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;11", "after": [{"result": {"duration": 64993000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 405000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 209000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2681000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 87}, {"val": " 123", "offset": 100}, {"val": "PROFILE_ID", "offset": 120}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 148}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 90808000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 404000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 55609000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: false", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "false", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 491000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 105049000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"CANCELLED\" for \"UNAUTHORIZED\" controller", "match": {"arguments": [{"val": "\"CANCELLED\"", "offset": 36}, {"val": "\"UNAUTHORIZED\"", "offset": 52}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 736000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:27.777Z", "before": [{"result": {"duration": 341000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 504000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 107, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;12", "after": [{"result": {"duration": 58867000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 372000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 342000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 4586000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 87}, {"val": " 123", "offset": 100}, {"val": "PROFILE_ID", "offset": 120}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 148}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 69639000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 741000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 59462000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: true", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "true", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 941000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 174748000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"FAILED_CHANNEL\" for \"UNAUTHORIZED\" controller", "match": {"arguments": [{"val": "\"FAILED_CHANNEL\"", "offset": 36}, {"val": "\"UNAUTHORIZED\"", "offset": 57}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 798000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:28.166Z", "before": [{"result": {"duration": 281000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 273000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 108, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;13", "after": [{"result": {"duration": 57647000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 214000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 199000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"phone\" information", "match": {"arguments": [{"val": "\"phone\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3734000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor OTP, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/OTP_authConfig.json", "match": {"arguments": [{"val": "OTP", "offset": 87}, {"val": " 123", "offset": 100}, {"val": "PROFILE_ID", "offset": 120}, {"val": "/dataShare/successCase/OTP_authConfig.json", "offset": 148}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 105055000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 459000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 89304000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: OTP and includeFactorConfig: false", "match": {"arguments": [{"val": "OTP", "offset": 56}, {"val": "false", "offset": 85}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 642000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 186242000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"FAILED_CHANNEL\" for \"UNAUTHORIZED\" controller", "match": {"arguments": [{"val": "\"FAILED_CHANNEL\"", "offset": 36}, {"val": "\"UNAUTHORIZED\"", "offset": 57}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 739000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:28.629Z", "before": [{"result": {"duration": 252000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 271000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 109, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;14", "after": [{"result": {"duration": 105790000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 250000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 181718000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 4812000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 87}, {"val": " 123", "offset": 107}, {"val": "PROFILE_ID", "offset": 127}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 155}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 118879000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 808000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 67182000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: true", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "true", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 718000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 136969000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"INELIGIBLE\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"INELIGIBLE\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 53}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 532000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:29.267Z", "before": [{"result": {"duration": 227000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 222000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 110, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;15", "after": [{"result": {"duration": 107689000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 235000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 114615000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2769000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 87}, {"val": " 123", "offset": 107}, {"val": "PROFILE_ID", "offset": 127}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 155}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 87275000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 494000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 56260000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: false", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "false", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 990000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 127160000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"INELIGIBLE\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"INELIGIBLE\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 53}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 595000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:29.780Z", "before": [{"result": {"duration": 289000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 267000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 111, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;16", "after": [{"result": {"duration": 106955000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 238000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 116914000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2971000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 87}, {"val": " 123", "offset": 107}, {"val": "PROFILE_ID", "offset": 127}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 155}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 88528000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 809000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 56780000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: true", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "true", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 708000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 120673000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"CANCELLED\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"CANCELLED\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 52}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 352000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:30.289Z", "before": [{"result": {"duration": 237000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 213000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 112, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;17", "after": [{"result": {"duration": 129790000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 238000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 114108000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3447000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 87}, {"val": " 123", "offset": 107}, {"val": "PROFILE_ID", "offset": 127}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 155}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 83629000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 497000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 55192000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: false", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "false", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 615000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 130221000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"CANCELLED\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"CANCELLED\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 52}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 833000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:30.833Z", "before": [{"result": {"duration": 247000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 219000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 113, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;18", "after": [{"result": {"duration": 138027000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 241000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 121037000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 5470000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 87}, {"val": " 123", "offset": 107}, {"val": "PROFILE_ID", "offset": 127}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 155}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 94168000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 656000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 59826000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: true", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "true", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 742000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 164645000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"FAILED_CHANNEL\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"FAILED_CHANNEL\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 57}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 1348000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:31.435Z", "before": [{"result": {"duration": 204000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 279000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 114, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;19", "after": [{"result": {"duration": 116779000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 208000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 115173000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2418000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 87}, {"val": " 123", "offset": 107}, {"val": "PROFILE_ID", "offset": 127}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 155}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 85432000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 463000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 55794000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: false", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "false", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 760000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 167900000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"FAILED_CHANNEL\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"FAILED_CHANNEL\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 57}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 470000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:31.997Z", "before": [{"result": {"duration": 371000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 540000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 115, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;20", "after": [{"result": {"duration": 109809000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 288000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 119944000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3816000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 87}, {"val": " 123", "offset": 107}, {"val": "PROFILE_ID", "offset": 127}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 155}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 88334000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 452000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 56241000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: true", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "true", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 634000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 135677000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"INELIGIB<PERSON>\" for \"UNAUTHORIZED\" controller", "match": {"arguments": [{"val": "\"INELIGIBLE\"", "offset": 36}, {"val": "\"UNAUTHORIZED\"", "offset": 53}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 627000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:32.532Z", "before": [{"result": {"duration": 403000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 334000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 116, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;21", "after": [{"result": {"duration": 109281000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 437000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 124236000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2883000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 87}, {"val": " 123", "offset": 107}, {"val": "PROFILE_ID", "offset": 127}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 155}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 88218000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 495000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 56428000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: false", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "false", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 776000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 132959000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"INELIGIB<PERSON>\" for \"UNAUTHORIZED\" controller", "match": {"arguments": [{"val": "\"INELIGIBLE\"", "offset": 36}, {"val": "\"UNAUTHORIZED\"", "offset": 53}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 619000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:33.065Z", "before": [{"result": {"duration": 275000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 231000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 117, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;22", "after": [{"result": {"duration": 110130000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 311000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 140217000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3978000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 87}, {"val": " 123", "offset": 107}, {"val": "PROFILE_ID", "offset": 127}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 155}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 104128000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 757000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 59449000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: true", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "true", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 1072000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 134682000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"CANCELLED\" for \"UNAUTHORIZED\" controller", "match": {"arguments": [{"val": "\"CANCELLED\"", "offset": 36}, {"val": "\"UNAUTHORIZED\"", "offset": 52}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 541000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:33.638Z", "before": [{"result": {"duration": 351000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 440000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 118, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;23", "after": [{"result": {"duration": 109987000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 242000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 123020000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3151000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 87}, {"val": " 123", "offset": 107}, {"val": "PROFILE_ID", "offset": 127}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 155}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 90378000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 486000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 56899000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: false", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "false", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 710000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 126382000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"CANCELLED\" for \"UNAUTHORIZED\" controller", "match": {"arguments": [{"val": "\"CANCELLED\"", "offset": 36}, {"val": "\"UNAUTHORIZED\"", "offset": 52}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 666000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:34.167Z", "before": [{"result": {"duration": 293000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 231000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 119, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;24", "after": [{"result": {"duration": 109181000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 269000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 121509000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 4030000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 87}, {"val": " 123", "offset": 107}, {"val": "PROFILE_ID", "offset": 127}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 155}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 89297000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 570000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 58743000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: true", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "true", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 672000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 126140000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"FAILED_CHANNEL\" for \"UNAUTHORIZED\" controller", "match": {"arguments": [{"val": "\"FAILED_CHANNEL\"", "offset": 36}, {"val": "\"UNAUTHORIZED\"", "offset": 57}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 467000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:34.698Z", "before": [{"result": {"duration": 348000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 287000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 120, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;25", "after": [{"result": {"duration": 108981000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 284000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 116971000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"device\" information", "match": {"arguments": [{"val": "\"device\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3420000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor DEVICE_BIO, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/DEVICE_BIO_authConfig.json", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 87}, {"val": " 123", "offset": 107}, {"val": "PROFILE_ID", "offset": 127}, {"val": "/dataShare/successCase/DEVICE_BIO_authConfig.json", "offset": 155}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 90173000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 580000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 57611000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: DEVICE_BIO and includeFactorConfig: false", "match": {"arguments": [{"val": "DEVICE_BIO", "offset": 56}, {"val": "false", "offset": 92}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 517000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 132441000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"FAILED_CHANNEL\" for \"UNAUTHORIZED\" controller", "match": {"arguments": [{"val": "\"FAILED_CHANNEL\"", "offset": 36}, {"val": "\"UNAUTHORIZED\"", "offset": 57}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 504000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:35.227Z", "before": [{"result": {"duration": 404000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 351000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 121, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;26", "after": [{"result": {"duration": 53960000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 422000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 386000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"facial\" information", "match": {"arguments": [{"val": "\"facial\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 4071000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor FACIAL, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/FACIAL_authConfig.json", "match": {"arguments": [{"val": "FACIAL", "offset": 87}, {"val": " 123", "offset": 103}, {"val": "PROFILE_ID", "offset": 123}, {"val": "/dataShare/successCase/FACIAL_authConfig.json", "offset": 151}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 101079000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 463000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 56899000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: FACIAL and includeFactorConfig: true", "match": {"arguments": [{"val": "FACIAL", "offset": 56}, {"val": "true", "offset": 88}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 542000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 113336000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"INELIGIBLE\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"INELIGIBLE\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 53}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 483000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:35.575Z", "before": [{"result": {"duration": 414000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 385000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 122, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;27", "after": [{"result": {"duration": 54020000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 235000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 193000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"facial\" information", "match": {"arguments": [{"val": "\"facial\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 6765000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor FACIAL, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/FACIAL_authConfig.json", "match": {"arguments": [{"val": "FACIAL", "offset": 87}, {"val": " 123", "offset": 103}, {"val": "PROFILE_ID", "offset": 123}, {"val": "/dataShare/successCase/FACIAL_authConfig.json", "offset": 151}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 77386000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 953000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 63717000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: FACIAL and includeFactorConfig: false", "match": {"arguments": [{"val": "FACIAL", "offset": 56}, {"val": "false", "offset": 88}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 747000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 117126000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"INELIGIBLE\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"INELIGIBLE\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 53}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 645000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:35.917Z", "before": [{"result": {"duration": 222000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 472000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 123, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;28", "after": [{"result": {"duration": 53664000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 370000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 372000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"facial\" information", "match": {"arguments": [{"val": "\"facial\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3269000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor FACIAL, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/FACIAL_authConfig.json", "match": {"arguments": [{"val": "FACIAL", "offset": 87}, {"val": " 123", "offset": 103}, {"val": "PROFILE_ID", "offset": 123}, {"val": "/dataShare/successCase/FACIAL_authConfig.json", "offset": 151}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 97661000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 485000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 57447000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: FACIAL and includeFactorConfig: true", "match": {"arguments": [{"val": "FACIAL", "offset": 56}, {"val": "true", "offset": 88}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 548000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 122470000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"CANCELLED\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"CANCELLED\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 52}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 557000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:36.276Z", "before": [{"result": {"duration": 349000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 381000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 124, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;29", "after": [{"result": {"duration": 52978000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 264000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 187000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"facial\" information", "match": {"arguments": [{"val": "\"facial\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2710000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor FACIAL, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/FACIAL_authConfig.json", "match": {"arguments": [{"val": "FACIAL", "offset": 87}, {"val": " 123", "offset": 103}, {"val": "PROFILE_ID", "offset": 123}, {"val": "/dataShare/successCase/FACIAL_authConfig.json", "offset": 151}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 98317000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 440000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 57767000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: FACIAL and includeFactorConfig: false", "match": {"arguments": [{"val": "FACIAL", "offset": 56}, {"val": "false", "offset": 88}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 526000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 118021000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"CANCELLED\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"CANCELLED\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 52}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 802000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:36.627Z", "before": [{"result": {"duration": 342000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 388000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 125, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;30", "after": [{"result": {"duration": 54666000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 308000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 258000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"facial\" information", "match": {"arguments": [{"val": "\"facial\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2965000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor FACIAL, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/FACIAL_authConfig.json", "match": {"arguments": [{"val": "FACIAL", "offset": 87}, {"val": " 123", "offset": 103}, {"val": "PROFILE_ID", "offset": 123}, {"val": "/dataShare/successCase/FACIAL_authConfig.json", "offset": 151}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 93203000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 627000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 58928000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: FACIAL and includeFactorConfig: true", "match": {"arguments": [{"val": "FACIAL", "offset": 56}, {"val": "true", "offset": 88}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 536000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 117431000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"FAILED_CHANNEL\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"FAILED_CHANNEL\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 57}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 790000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:36.972Z", "before": [{"result": {"duration": 505000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 348000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 126, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;31", "after": [{"result": {"duration": 53557000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 233000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 192000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"facial\" information", "match": {"arguments": [{"val": "\"facial\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3325000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor FACIAL, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/FACIAL_authConfig.json", "match": {"arguments": [{"val": "FACIAL", "offset": 87}, {"val": " 123", "offset": 103}, {"val": "PROFILE_ID", "offset": 123}, {"val": "/dataShare/successCase/FACIAL_authConfig.json", "offset": 151}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 92668000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 431000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 57396000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: FACIAL and includeFactorConfig: false", "match": {"arguments": [{"val": "FACIAL", "offset": 56}, {"val": "false", "offset": 88}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 605000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 116956000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"FAILED_CHANNEL\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"FAILED_CHANNEL\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 57}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 435000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:37.315Z", "before": [{"result": {"duration": 410000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 397000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 127, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;32", "after": [{"result": {"duration": 53438000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 247000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 176000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"passcode\" information", "match": {"arguments": [{"val": "\"passcode\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3292000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor PASSCODE, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/PASS_CODE_authConfig.json", "match": {"arguments": [{"val": "PASSCODE", "offset": 87}, {"val": " 123", "offset": 105}, {"val": "PROFILE_ID", "offset": 125}, {"val": "/dataShare/successCase/PASS_CODE_authConfig.json", "offset": 153}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 96132000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 501000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 58560000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: PASSCODE and includeFactorConfig: true", "match": {"arguments": [{"val": "PASSCODE", "offset": 56}, {"val": "true", "offset": 90}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 657000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 113774000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"INELIGIBLE\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"INELIGIBLE\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 53}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 681000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:37.661Z", "before": [{"result": {"duration": 333000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 441000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 128, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;33", "after": [{"result": {"duration": 54459000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 261000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 181000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"passcode\" information", "match": {"arguments": [{"val": "\"passcode\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3000000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor PASSCODE, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/PASS_CODE_authConfig.json", "match": {"arguments": [{"val": "PASSCODE", "offset": 87}, {"val": " 123", "offset": 105}, {"val": "PROFILE_ID", "offset": 125}, {"val": "/dataShare/successCase/PASS_CODE_authConfig.json", "offset": 153}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 96283000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 410000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 56662000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: PASSCODE and includeFactorConfig: false", "match": {"arguments": [{"val": "PASSCODE", "offset": 56}, {"val": "false", "offset": 90}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 523000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 118615000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"INELIGIBLE\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"INELIGIBLE\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 53}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 667000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:38.010Z", "before": [{"result": {"duration": 320000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 250000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 129, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;34", "after": [{"result": {"duration": 52852000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 237000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 182000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"passcode\" information", "match": {"arguments": [{"val": "\"passcode\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 4133000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor PASSCODE, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/PASS_CODE_authConfig.json", "match": {"arguments": [{"val": "PASSCODE", "offset": 87}, {"val": " 123", "offset": 105}, {"val": "PROFILE_ID", "offset": 125}, {"val": "/dataShare/successCase/PASS_CODE_authConfig.json", "offset": 153}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 91509000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 500000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 56465000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: PASSCODE and includeFactorConfig: true", "match": {"arguments": [{"val": "PASSCODE", "offset": 56}, {"val": "true", "offset": 90}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 669000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 117146000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"CANCELLED\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"CANCELLED\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 52}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 578000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:38.350Z", "before": [{"result": {"duration": 266000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 297000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 130, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;35", "after": [{"result": {"duration": 53809000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 285000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 185000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"passcode\" information", "match": {"arguments": [{"val": "\"passcode\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2876000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor PASSCODE, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/PASS_CODE_authConfig.json", "match": {"arguments": [{"val": "PASSCODE", "offset": 87}, {"val": " 123", "offset": 105}, {"val": "PROFILE_ID", "offset": 125}, {"val": "/dataShare/successCase/PASS_CODE_authConfig.json", "offset": 153}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 95135000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 459000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 56226000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: PASSCODE and includeFactorConfig: false", "match": {"arguments": [{"val": "PASSCODE", "offset": 56}, {"val": "false", "offset": 90}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 523000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 132248000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"CANCELLED\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"CANCELLED\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 52}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 403000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:38.711Z", "before": [{"result": {"duration": 366000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 318000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 131, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;36", "after": [{"result": {"duration": 54583000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 280000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 282000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"passcode\" information", "match": {"arguments": [{"val": "\"passcode\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 3121000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor PASSCODE, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/PASS_CODE_authConfig.json", "match": {"arguments": [{"val": "PASSCODE", "offset": 87}, {"val": " 123", "offset": 105}, {"val": "PROFILE_ID", "offset": 125}, {"val": "/dataShare/successCase/PASS_CODE_authConfig.json", "offset": 153}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 92675000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 475000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 57394000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: PASSCODE and includeFactorConfig: true", "match": {"arguments": [{"val": "PASSCODE", "offset": 56}, {"val": "true", "offset": 90}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 558000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 122923000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"FAILED_CHANNEL\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"FAILED_CHANNEL\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 57}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 891000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}, {"start_timestamp": "2025-07-17T03:21:39.063Z", "before": [{"result": {"duration": 468000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SessionUpdatesAPICucumberSteps.init()"}}, {"result": {"duration": 330000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.init()"}}], "line": 132, "name": "Successfully initialize step-up session and perform fallback", "description": "", "id": "component-test-for-success-case-with-step-up-api;successfully-initialize-step-up-session-and-perform-fallback;;37", "after": [{"result": {"duration": 58930000, "status": "passed"}, "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.cleanup()"}}], "type": "scenario", "keyword": "Scenario Outline", "steps": [{"result": {"duration": 268000, "status": "passed"}, "line": 85, "name": "A user profile exists in the system", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.aUserProfileExistsInTheSystem()"}, "keyword": "Given "}, {"result": {"duration": 200000, "status": "passed"}, "line": 86, "name": "The profile contains valid \"passcode\" information", "match": {"arguments": [{"val": "\"passcode\"", "offset": 27}], "location": "com.tyme.tymex.stepupauth.cucumber.GivenClassCucumber.theProfileContainsValidInformation(java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 2569000, "status": "passed"}, "line": 87, "name": "I build the request body for initializing a step-up session with fallback using factor PASSCODE, flow id: 123, profile type: PROFILE_ID, and authConfig: /dataShare/successCase/PASS_CODE_authConfig.json", "match": {"arguments": [{"val": "PASSCODE", "offset": 87}, {"val": " 123", "offset": 105}, {"val": "PROFILE_ID", "offset": 125}, {"val": "/dataShare/successCase/PASS_CODE_authConfig.json", "offset": 153}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.processBuildInitRequestWithFallback(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"}, "keyword": "And "}, {"result": {"duration": 100541000, "status": "passed"}, "line": 88, "name": "The customer go to init step up", "match": {"location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.GoToRequestToAPIAPIToReqDescription()"}, "keyword": "And "}, {"result": {"duration": 553000, "status": "passed"}, "line": 89, "name": "The API should be return to status code: 201", "match": {"arguments": [{"val": "201", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 57577000, "status": "passed"}, "line": 90, "name": "When the customer go to validation step up with factor: PASSCODE and includeFactorConfig: false", "match": {"arguments": [{"val": "PASSCODE", "offset": 56}, {"val": "false", "offset": 90}], "location": "com.tyme.tymex.stepupauth.cucumber.DemoValidationStepUp.whenTheCustomerGoToValidationStepUpWithFactorValidateFactorAndIncludeFactorConfigIncludeFactorConfig(java.lang.String,java.lang.Boolean)"}, "keyword": "And "}, {"result": {"duration": 608000, "status": "passed"}, "line": 91, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}, {"result": {"duration": 128231000, "status": "passed"}, "line": 92, "name": "I call the fallback API with status \"FAILED_CHANNEL\" for \"AUTHORIZED\" controller", "match": {"arguments": [{"val": "\"FAILED_CHANNEL\"", "offset": 36}, {"val": "\"AUTHORIZED\"", "offset": 57}], "location": "com.tyme.tymex.stepupauth.cucumber.StepUpFallbackAPICucumberSteps.iCallTheFallbackAPIWithStatusForController(java.lang.String,java.lang.String)"}, "keyword": "When "}, {"result": {"duration": 818000, "status": "passed"}, "line": 93, "name": "The API should be return to status code: 200", "match": {"arguments": [{"val": "200", "offset": 41}], "location": "com.tyme.tymex.stepupauth.cucumber.SuccessCaseComponent.theAPIShouldBeReturnToStatusCodeStatusCode(int)"}, "keyword": "Then "}], "tags": [{"name": "@StepUpPassCase"}, {"name": "@StepUpFallback"}]}], "name": "Component test for success case with Step-Up API", "description": "", "id": "component-test-for-success-case-with-step-up-api", "keyword": "Feature", "uri": "classpath:features/component/DemoInitStepUp.feature", "tags": [{"name": "@StepUpPassCase", "type": "Tag", "location": {"line": 1, "column": 1}}]}]