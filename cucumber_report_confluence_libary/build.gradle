plugins {
    id 'java-library'
    id 'application'
    id 'maven-publish'
}

group = 'com.library'
version = '1.0.0'

application {
    mainClass = 'com.library.example.TestPermissions'
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
    withJavadocJar()
    withSourcesJar()
}

repositories {
    mavenCentral()
}

dependencies {
    // Cucumber dependencies
    api 'io.cucumber:cucumber-java:7.14.0'
    api 'io.cucumber:cucumber-junit:7.14.0'

    // HTTP Client for Confluence API
    implementation 'org.apache.httpcomponents.client5:httpclient5:5.2.1'
    implementation 'org.apache.httpcomponents.core5:httpcore5:5.2.2'

    // JSON processing
    implementation 'com.fasterxml.jackson.core:jackson-core:2.15.2'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.15.2'

    // Logging
    implementation 'org.slf4j:slf4j-api:2.0.7'
    implementation 'ch.qos.logback:logback-classic:1.4.11'

    // Utilities
    implementation 'org.apache.commons:commons-lang3:3.18.0'
    implementation 'commons-io:commons-io:2.14.0'

    // Test dependencies
    testImplementation 'org.junit.jupiter:junit-jupiter:5.9.3'
    testImplementation 'org.mockito:mockito-core:5.4.0'
    testImplementation 'org.mockito:mockito-junit-jupiter:5.4.0'
    testImplementation 'org.assertj:assertj-core:3.24.2'
}

test {
    useJUnitPlatform()
}

jar {
    archiveBaseName = 'cucumber-report-confluence-library'
    archiveVersion = version

    manifest {
        attributes(
            'Implementation-Title': 'Cucumber Report Confluence Library',
            'Implementation-Version': version,
            'Implementation-Vendor': 'Library Team'
        )
    }
}

publishing {
    publications {
        maven(MavenPublication) {
            from components.java

            pom {
                name = 'Cucumber Report Confluence Library'
                description = 'A library for generating Cucumber reports and publishing to Confluence'
                url = 'https://github.com/your-org/cucumber-report-confluence-library'

                licenses {
                    license {
                        name = 'The Apache License, Version 2.0'
                        url = 'http://www.apache.org/licenses/LICENSE-2.0.txt'
                    }
                }

                developers {
                    developer {
                        id = 'ducpham'
                        name = 'ducpham'
                        email = '<EMAIL>'
                    }
                }
            }
        }
    }
}
